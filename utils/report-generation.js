const XLSX = require("xlsx");
const fs = require("fs");
const pdf = require("html-pdf");
const XLSX_STYLE = require("xlsx-style");

/**
 * Export JSON data to a templated Excel file with header, title, and footer.
 * Format is similar to chess tournament results with proper borders and styling.
 *
 * @param {Object} options - The options for Excel export
 * @param {Array<Object>} options.data - The JSON data to convert
 * @param {string} options.sheetName - The name of the Excel sheet (tab)
 * @param {string} options.title - The title of the report
 * @param {string} options.reportType - The type of report (e.g., "Tournament Players", "Club List")
 */
// const exportToExcel = ({ data, sheetName = "Sheet1", title = "", reportType = "" }) => {
//   try {
//     // Validate data
//     if (!Array.isArray(data) || data.length === 0) {
//       return { success: false, error: "Data must be a non-empty array of objects." };
//     }

//     // Create workbook
//     const workbook = XLSX.utils.book_new();

//     // Create a new worksheet
//     const worksheet = XLSX.utils.aoa_to_sheet([]);

//     // Add the worksheet to the workbook
//     XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

//     // Current date for "Last Updated" field
//     const currentDate = new Date().toLocaleDateString();

//     // Define header content
//     const headerRows = [
//       ["From the ChessBrigade.com"],
//       [title || reportType || "Chess Tournament Report"],
//       [`Last update ${currentDate}`],
//       [""] // Empty row before the table starts
//     ];

//     // Add header to worksheet
//     XLSX.utils.sheet_add_aoa(worksheet, headerRows, { origin: "A1" });

//     // Get column headers (keys from first data object)
//     const headers = Object.keys(data[0]);

//     // Format headers to be more readable (capitalize first letter, replace camelCase with spaces)
//     const formattedHeaders = headers.map(header => {
//       // Convert camelCase to Title Case With Spaces
//       return header
//         .replace(/([A-Z])/g, ' $1') // Insert space before capital letters
//         .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
//         .trim();
//     });

//     // Add column headers row
//     XLSX.utils.sheet_add_aoa(worksheet, [formattedHeaders], { origin: `A${headerRows.length + 1}` });

//     // Add data rows
//     const dataValues = data.map(item => headers.map(key => item[key]));
//     XLSX.utils.sheet_add_aoa(worksheet, dataValues, { origin: `A${headerRows.length + 2}` });

//     // Add footer
//     const footerRow = headerRows.length + dataValues.length + 3;
//     XLSX.utils.sheet_add_aoa(worksheet, [[""], ["Chess-Tournament-Results-Server: ChessBrigade.com"]], { origin: `A${footerRow}` });

//     // Set column widths
//     const columnWidths = headers.map(header => {
//       // Set minimum width based on header length
//       return { wch: Math.max(header.length * 1.5, 10) };
//     });
//     worksheet['!cols'] = columnWidths;

//     // Apply styles
//     // Get the range of cells in the worksheet
//     const range = XLSX.utils.decode_range(worksheet['!ref']);

//     // Style the header section
//     for (let r = 0; r < headerRows.length; r++) {
//       const cellRef = XLSX.utils.encode_cell({ r, c: 0 });
//       if (!worksheet[cellRef]) continue;

//       if (!worksheet[cellRef].s) worksheet[cellRef].s = {};

//       // First row (ChessBrigade.com) - make it purple like the example
//       if (r === 0) {
//         worksheet[cellRef].s = {
//           font: { bold: true, sz: 12, color: { rgb: "800080" } }, // Purple color
//           alignment: { horizontal: "left" },
//           fill: { fgColor: { rgb: "F5F5F5" } } // Light gray background
//         };
//       }
//       // Title row - make it bold and black with light blue background
//       else if (r === 1) {
//         worksheet[cellRef].s = {
//           font: { bold: true, sz: 14, color: { rgb: "000000" } },
//           alignment: { horizontal: "left" },
//           fill: { fgColor: { rgb: "E6F0FF" } } // Light blue background
//         };
//       }
//       // Last updated date - make it italic with very light gray background
//       else if (r === 2) {
//         worksheet[cellRef].s = {
//           font: { italic: true, sz: 10 },
//           alignment: { horizontal: "left" },
//           fill: { fgColor: { rgb: "FAFAFA" } } // Very light gray background
//         };
//       }
//     }

//     // Style the column headers row - make them all have borders and background color
//     const headerRowIndex = headerRows.length;
//     for (let c = 0; c <= range.e.c; c++) {
//       const cellRef = XLSX.utils.encode_cell({ r: headerRowIndex, c });
//       if (!worksheet[cellRef]) continue;

//       if (!worksheet[cellRef].s) worksheet[cellRef].s = {};
//       worksheet[cellRef].s = {
//         font: { bold: true, color: { rgb: "FFFFFF" } }, // White text
//         border: {
//           top: { style: "thin", color: { rgb: "000000" } },
//           bottom: { style: "thin", color: { rgb: "000000" } },
//           left: { style: "thin", color: { rgb: "000000" } },
//           right: { style: "thin", color: { rgb: "000000" } }
//         },
//         fill: { fgColor: { rgb: "4F81BD" } }, // Blue background
//         alignment: { horizontal: "center" }
//       };
//     }

//     // Style all data cells with borders and alternating row colors like a proper table
//     for (let r = headerRowIndex + 1; r <= range.e.r - 2; r++) {
//       // Determine row background color - alternate between white and light gray
//       const isAlternateRow = (r - headerRowIndex) % 2 === 0;
//       const rowBgColor = isAlternateRow ? "F2F2F2" : "FFFFFF"; // Light gray or white

//       for (let c = 0; c <= range.e.c; c++) {
//         const cellRef = XLSX.utils.encode_cell({ r, c });
//         if (!worksheet[cellRef]) continue;

//         if (!worksheet[cellRef].s) worksheet[cellRef].s = {};

//         // Base style for all cells - borders and alignment
//         const baseStyle = {
//           border: {
//             top: { style: "thin", color: { rgb: "000000" } },
//             bottom: { style: "thin", color: { rgb: "000000" } },
//             left: { style: "thin", color: { rgb: "000000" } },
//             right: { style: "thin", color: { rgb: "000000" } }
//           },
//           alignment: { horizontal: "center" },
//           fill: { fgColor: { rgb: rowBgColor } }
//         };

//         // Apply the base style
//         worksheet[cellRef].s = baseStyle;

//         // Special formatting for specific cell types
//         if (worksheet[cellRef].v !== undefined) {
//           const value = worksheet[cellRef].v.toString();

//           // Match result cells (like 1-0, 0-1) and color them blue
//           if (value === '1-0' || value === '0-1' || value === '½-½' || value === '0-0') {
//             worksheet[cellRef].s.font = { color: { rgb: "0000FF" } }; // Blue color
//           }

//           // Format numeric values to be right-aligned
//           if (!isNaN(value) && typeof worksheet[cellRef].v === 'number') {
//             worksheet[cellRef].s.alignment = { horizontal: "right" };
//           }

//           // Format date-like strings
//           if (value.match(/^\d{4}-\d{2}-\d{2}/) || value.match(/^\d{2}\/\d{2}\/\d{4}/)) {
//             worksheet[cellRef].s.font = { color: { rgb: "006400" } }; // Dark green for dates
//           }

//           // Highlight cells with "Yes", "No", "True", "False" values
//           if (['yes', 'no', 'true', 'false'].includes(value.toLowerCase())) {
//             worksheet[cellRef].s.font = {
//               bold: true,
//               color: { rgb: value.toLowerCase() === 'yes' || value.toLowerCase() === 'true' ? "008000" : "FF0000" }
//             };
//           }
//         }
//       }
//     }

//     // Style the footer
//     const footerCellRef = XLSX.utils.encode_cell({ r: range.e.r, c: 0 });
//     if (worksheet[footerCellRef]) {
//       if (!worksheet[footerCellRef].s) worksheet[footerCellRef].s = {};
//       worksheet[footerCellRef].s = {
//         font: { bold: true, color: { rgb: "800080" } }, // Purple color like the header
//         alignment: { horizontal: "left" },
//         fill: { fgColor: { rgb: "F5F5F5" } }, // Light gray background
//         border: {
//           top: { style: "thin", color: { rgb: "000000" } }
//         }
//       };
//     }

//     // Merge cells for header and footer
//     if (!worksheet['!merges']) worksheet['!merges'] = [];

//     // Merge header cells
//     for (let r = 0; r < headerRows.length; r++) {
//       worksheet['!merges'].push({
//         s: { r, c: 0 },
//         e: { r, c: range.e.c }
//       });
//     }

//     // Merge footer cells
//     worksheet['!merges'].push({
//       s: { r: range.e.r, c: 0 },
//       e: { r: range.e.r, c: range.e.c }
//     });

//     // Write the workbook to buffer
//     const excelBuffer = XLSX.write(workbook, {
//       bookType: "xlsx",
//       type: "buffer",
//       cellStyles: true // This is important to include cell styling
//     });

//     return { success: true, buffer: excelBuffer };

//   } catch (error) {
//     console.error("Excel export failed:", error);
//     return { success: false, error: error.message || "Unknown error occurred." };
//   }
// };

// 1. First, make sure to install both packages:
// npm install xlsx xlsx-style

const exportToExcel = ({
  data,
  sheetName = "Sheet1",
  title = "",
  reportType = "",
}) => {
  try {
    // Validate data
    if (!Array.isArray(data) || data.length === 0) {
      return {
        success: false,
        error: "Data must be a non-empty array of objects.",
      };
    }

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Create a new worksheet
    const worksheet = XLSX.utils.aoa_to_sheet([]);

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // Current date for "Last Updated" field
    const currentDate = new Date().toLocaleDateString();

    // Define header content
    const headerRows = [
      ["From the ChessBrigade.com"],
      [title || reportType || "Chess Tournament Report"],
      [`Last update ${currentDate}`],
      [""], // Empty row before the table starts
    ];

    // Add header to worksheet
    XLSX.utils.sheet_add_aoa(worksheet, headerRows, { origin: "A1" });

    // Get column headers (keys from first data object)
    const headers = Array.from(
      new Set(data.flatMap((item) => Object.keys(item)))
    );

    // Format headers to be more readable (capitalize first letter, replace camelCase with spaces)
    const formattedHeaders = headers.map((header) => {
      // Convert camelCase to Title Case With Spaces
      return header
        .replace(/([A-Z])/g, " $1") // Insert space before capital letters
        .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
        .trim();
    });

    // Add column headers row
    XLSX.utils.sheet_add_aoa(worksheet, [formattedHeaders], {
      origin: `A${headerRows.length + 1}`,
    });

    // Add data rows
    const dataValues = data.map((item) => headers.map((key) => item[key]));
    XLSX.utils.sheet_add_aoa(worksheet, dataValues, {
      origin: `A${headerRows.length + 2}`,
    });

    // Add footer
    const footerRow = headerRows.length + dataValues.length + 3;
    XLSX.utils.sheet_add_aoa(worksheet, [[""], ["@ChessBrigade.com"]], {
      origin: `A${footerRow}`,
    });

    // Set column widths
    const columnWidths = headers.map((header) => {
      // Set minimum width based on header length
      return { wch: Math.max(header.length * 1.5, 10) };
    });
    worksheet["!cols"] = columnWidths;

    // Apply styles
    // Get the range of cells in the worksheet
    const range = XLSX.utils.decode_range(worksheet["!ref"]);

    // Style the header section
    for (let r = 0; r < headerRows.length; r++) {
      const cellRef = XLSX.utils.encode_cell({ r, c: 0 });
      if (!worksheet[cellRef]) continue;

      if (!worksheet[cellRef].s) worksheet[cellRef].s = {};

      // First row (ChessBrigade.com) - make it purple like the example
      if (r === 0) {
        worksheet[cellRef].s = {
          font: { bold: true, sz: 12, color: { rgb: "800080" } }, // Purple color
          alignment: { horizontal: "left" },
          fill: { patternType: "solid", fgColor: { rgb: "F5F5F5" } }, // Light gray background - note patternType added
        };
      }
      // Title row - make it bold and black with light blue background
      else if (r === 1) {
        worksheet[cellRef].s = {
          font: { bold: true, sz: 14, color: { rgb: "000000" } },
          alignment: { horizontal: "left" },
          fill: { patternType: "solid", fgColor: { rgb: "E6F0FF" } }, // Light blue background - note patternType added
        };
      }
      // Last updated date - make it italic with very light gray background
      else if (r === 2) {
        worksheet[cellRef].s = {
          font: { italic: true, sz: 10 },
          alignment: { horizontal: "left" },
          fill: { patternType: "solid", fgColor: { rgb: "FAFAFA" } }, // Very light gray background - note patternType added
        };
      }
    }

    // Style the column headers row - make them all have borders and background color
    const headerRowIndex = headerRows.length;
    for (let c = 0; c <= range.e.c; c++) {
      const cellRef = XLSX.utils.encode_cell({ r: headerRowIndex, c });
      if (!worksheet[cellRef]) continue;

      if (!worksheet[cellRef].s) worksheet[cellRef].s = {};
      worksheet[cellRef].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } }, // White text
        border: {
          top: { style: "thin", color: { rgb: "000000" } },
          bottom: { style: "thin", color: { rgb: "000000" } },
          left: { style: "thin", color: { rgb: "000000" } },
          right: { style: "thin", color: { rgb: "000000" } },
        },
        fill: { patternType: "solid", fgColor: { rgb: "4F81BD" } }, // Blue background - note patternType added
        alignment: { horizontal: "center" },
      };
    }

    // Style all data cells with borders and alternating row colors like a proper table
    for (let r = headerRowIndex + 1; r <= range.e.r - 2; r++) {
      // Determine row background color - alternate between white and light gray
      const isAlternateRow = (r - headerRowIndex) % 2 === 0;
      const rowBgColor = isAlternateRow ? "F2F2F2" : "FFFFFF"; // Light gray or white

      for (let c = 0; c <= range.e.c; c++) {
        const cellRef = XLSX.utils.encode_cell({ r, c });
        if (!worksheet[cellRef]) continue;

        if (!worksheet[cellRef].s) worksheet[cellRef].s = {};

        // Base style for all cells - borders and alignment
        const baseStyle = {
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } },
          },
          alignment: { horizontal: "center" },
          fill: { patternType: "solid", fgColor: { rgb: rowBgColor } }, // Note patternType added
        };

        // Apply the base style
        worksheet[cellRef].s = baseStyle;

        // Special formatting for specific cell types
        if (worksheet[cellRef].v !== undefined) {
          const value = worksheet[cellRef].v.toString();

          // Match result cells (like 1-0, 0-1) and color them blue
          if (
            value === "1-0" ||
            value === "0-1" ||
            value === "½-½" ||
            value === "0-0"
          ) {
            worksheet[cellRef].s.font = { color: { rgb: "0000FF" } }; // Blue color
          }

          // Format numeric values to be right-aligned
          if (!isNaN(value) && typeof worksheet[cellRef].v === "number") {
            worksheet[cellRef].s.alignment = { horizontal: "right" };
          }

          // Format date-like strings
          if (
            value.match(/^\d{4}-\d{2}-\d{2}/) ||
            value.match(/^\d{2}\/\d{2}\/\d{4}/)
          ) {
            worksheet[cellRef].s.font = { color: { rgb: "006400" } }; // Dark green for dates
          }

          // Highlight cells with "Yes", "No", "True", "False" values
          if (["yes", "no", "true", "false"].includes(value.toLowerCase())) {
            worksheet[cellRef].s.font = {
              bold: true,
              color: {
                rgb:
                  value.toLowerCase() === "yes" ||
                  value.toLowerCase() === "true"
                    ? "008000"
                    : "FF0000",
              },
            };
          }
        }
      }
    }

    // Style the footer
    const footerCellRef = XLSX.utils.encode_cell({ r: range.e.r, c: 0 });
    if (worksheet[footerCellRef]) {
      if (!worksheet[footerCellRef].s) worksheet[footerCellRef].s = {};
      worksheet[footerCellRef].s = {
        font: { bold: true, color: { rgb: "800080" } }, // Purple color like the header
        alignment: { horizontal: "left" },
        fill: { patternType: "solid", fgColor: { rgb: "F5F5F5" } }, // Light gray background - note patternType added
        border: {
          top: { style: "thin", color: { rgb: "000000" } },
        },
      };
    }

    // Merge cells for header and footer
    if (!worksheet["!merges"]) worksheet["!merges"] = [];

    // Merge header cells
    for (let r = 0; r < headerRows.length; r++) {
      worksheet["!merges"].push({
        s: { r, c: 0 },
        e: { r, c: range.e.c },
      });
    }

    // Merge footer cells
    worksheet["!merges"].push({
      s: { r: range.e.r, c: 0 },
      e: { r: range.e.r, c: range.e.c },
    });

    // Write the workbook to buffer - Using XLSX_STYLE instead of XLSX for writing with styles
    const excelBuffer = XLSX_STYLE.write(workbook, {
      bookType: "xlsx",
      type: "buffer",
      cellStyles: true,
    });

    return { success: true, buffer: excelBuffer };
  } catch (error) {
    console.error("Excel export failed:", error);
    return {
      success: false,
      error: error.message || "Unknown error occurred.",
    };
  }
};

const exportToExcelXMLForSwiss = ({ data, rootElement = "Players" }) => {
  try {
    // Validate data
    if (!Array.isArray(data) || data.length === 0) {
      return {
        success: false,
        error: "Data must be a non-empty array of objects.",
      };
    }

    // Escape XML special characters for attributes
    const escapeXMLAttribute = (str) => {
      if (str == null) return "";
      return String(str)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#39;");
    };

    // Map your data structure to Swiss format
    const mapToSwissFormat = (item, index) => {
      // Generate unique PlayerUniqueId if not provided
      const playerUniqueId = item["PlayerUniqueId"] || index + 1;

      // Map sex to single character
      const formatGender = (sex) => {
        if (!sex) return "";
        return sex.toLowerCase().charAt(0).toUpperCase(); // 'm' or 'f'
      };

      return {
        PlayerUniqueId: playerUniqueId,
        Lastname: item["Last Name"] || "",
        Firstname: item["First Name"] || "",
        AcademicTitle: item["title"] || "",
        Federation: item["Fed"] || "IND",
        Rating: item["Rtg.Int"] || "",
        Birthday: item["Birth"] || "",
        Title: item["Title"] || "",
        FIDEId: item["FIDE No"] || "",
        Boardnumber: item["Boardnumber"] || "",
        Gender: formatGender(item["Sex"]) || "",
        TeamUniqueId: item["TeamUniqueId"] || "",
        Typ: item["Type"] || "", // This should map from your data
        Group: item["Group"] || "", // This should map from your data
        Source: item["Source"] || "FIDE",
        Club: item["Club"] || "", // This should map from your data
        IDno: item["ID no"] || "",
      };
    };

    // Build Swiss XML format
    let xml = `<?xml version="1.0" encoding="UTF-8"?>\n<${rootElement}>\n`;

    data.forEach((item, index) => {
      const swissData = mapToSwissFormat(item, index);

      xml += `<Player`;

      // Add all attributes - MODIFIED: Include even empty values for Group, Type, Club
      Object.entries(swissData).forEach(([key, value]) => {
        // Always include Group, Type, Club even if empty
        if (
          value !== "" ||
          key === "Group" ||
          key === "Type" ||
          key === "Club"
        ) {
          xml += ` ${key}="${escapeXMLAttribute(value)}"`;
        }
      });

      xml += ` />\n`;
    });

    xml += `</${rootElement}>`;

    // Convert to buffer
    const buffer = Buffer.from(xml, "utf8");

    return { success: true, buffer, xml };
  } catch (error) {
    console.error("Swiss XML export failed:", error);
    return {
      success: false,
      error: error.message || "Unknown error occurred.",
    };
  }
};
// Example usage

module.exports = { exportToExcel, exportToExcelXMLForSwiss };

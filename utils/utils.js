
/**
 * Generate an abbreviation from a name with specified length
 *
 * @param name The name to abbreviate
 * @param length The desired abbreviation length
 * @returns An abbreviation in lowercase
 */
function generateAbbreviation(name, length) {
  // Normalize the name: convert to lowercase and remove special characters
  const normalized = name.toLowerCase().replace(/[^\w\s]/g, "");

  // Split into words
  const words = normalized.split(/\s+/).filter((w) => w.length > 0);

  // If we have enough words, use first letter of each word
  if (words.length >= length) {
    return words
      .slice(0, length)
      .map((w) => w[0])
      .join("");
  }

  // If we don't have enough words, use first letters plus more letters from first word
  let abbr = words.map((w) => w[0]).join("");

  // If still not enough, add more characters from the first word
  if (abbr.length < length && words.length > 0) {
    const firstWord = words[0];
    abbr += firstWord.substring(1, length - abbr.length + 1);
  }

  // Pad with 'x' if necessary (should rarely happen)
  while (abbr.length < length) {
    abbr += "x";
  }

  return abbr;
}

module.exports = {
  generateAbbreviation,
};

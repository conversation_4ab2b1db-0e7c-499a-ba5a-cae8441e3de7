const { Sequelize } = require("sequelize");
const { database, config } = require("./config");
const initModels = require("../models/model");

const sequelizeOptions = {
  dialectOptions: config.database.dialectOptions,
  // Disable all SQL query logging - only show errors
  logging: false,
  // Global underscore naming convention for all models
  define: {
    underscored: true,
    timestamps: true,
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};

const sequelize = new Sequelize(config.databaseUrl, {
  dialect: "postgres",
  ...sequelizeOptions,
});

const models = initModels(sequelize);
Object.keys(models).forEach((modelName) => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

const connectDb = async (options = {}) => {
  try {
    await sequelize.authenticate();
    console.log("Database connected.");

    // Only sync if one of the specific options is provided
    if (options.force) {
      await sequelize.sync({ force: true });
      console.log("Database synchronized: All tables recreated (force).");
    } else if (options.alter) {
      await sequelize.sync({ alter: true });
      console.log("Database synchronized: Tables altered to match models.");
    } else if (options.sync) {
      await sequelize.sync();
      console.log(
        "Database synchronized: Tables created if they didn't exist."
      );
    }

    // Only seed if explicitly requested
    if (options.seed) {
      const isProduction = config.node_env === "production";
      if (!isProduction) {
        const { seedDatabase } = require("../utils/seed/seed");
        await seedDatabase(models, { ignoreDuplicates: true });
        console.log("Database seeded successfully.");
      } else {
        console.log("Production mode: Skipping database seeding.");
      }
    }
  } catch (error) {
    console.error("Unable to connect to the database:", error);
    throw error;
  }
};


module.exports = { sequelize, connectDb, models };

'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('bank_details', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      club_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      bank_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      account_number: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      branch_ifsc_code: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      branch_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      bank_account_type: {
        type: Sequelize.ENUM(
          'savings',
          'current',
          'salary',
          'fixed_deposit',
          'recurring_deposit'
        ),
        allowNull: false,
      },
      bank_account_holder_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {},
      },
      is_locked: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      is_verified: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      verification_attempts: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false,
      },
      last_verification_attempt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      verified_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true, // For paranoid (soft delete)
      },
      payout_enabled: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
    });

    // Add indexes
    await queryInterface.addIndex('bank_details', {
      fields: ['club_id'],
      unique: true,
      name: 'bank_details_club_id_unique',
    });

    await queryInterface.addIndex('bank_details', {
      fields: ['is_verified'],
      name: 'bank_details_is_verified_idx',
    });

    await queryInterface.addIndex('bank_details', {
      fields: ['is_locked'],
      name: 'bank_details_is_locked_idx',
    });

    await queryInterface.addIndex('bank_details', {
      fields: ['branch_ifsc_code'],
      name: 'bank_details_branch_ifsc_code_idx',
    });

    await queryInterface.addIndex('bank_details', {
      fields: ['verification_attempts'],
      name: 'bank_details_verification_attempts_idx',
    });

    await queryInterface.addIndex('bank_details', {
      fields: ['last_verification_attempt'],
      name: 'bank_details_last_verification_attempt_idx',
    });

    // Add composite index for verification eligibility
    await queryInterface.addIndex('bank_details', {
      fields: ['is_verified', 'is_locked', 'verification_attempts'],
      name: 'bank_details_verification_eligibility_idx',
    });

    // Add index for deleted_at (paranoid queries)
    await queryInterface.addIndex('bank_details', {
      fields: ['deleted_at'],
      name: 'bank_details_deleted_at_idx',
    });
    
  },

  async down(queryInterface, Sequelize) {
    // Drop indexes first
    await queryInterface.removeIndex('bank_details', 'bank_details_club_id_unique');
    await queryInterface.removeIndex('bank_details', 'bank_details_is_verified_idx');
    await queryInterface.removeIndex('bank_details', 'bank_details_is_locked_idx');
    await queryInterface.removeIndex('bank_details', 'bank_details_branch_ifsc_code_idx');
    await queryInterface.removeIndex('bank_details', 'bank_details_verification_attempts_idx');
    await queryInterface.removeIndex('bank_details', 'bank_details_last_verification_attempt_idx');
    await queryInterface.removeIndex('bank_details', 'bank_details_verification_eligibility_idx');
    await queryInterface.removeIndex('bank_details', 'bank_details_deleted_at_idx');

    // Drop ENUM type
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_bank_details_bank_account_type";');

    // Drop table
    await queryInterface.dropTable('bank_details');
  }
};
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('club_details', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      profile_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      club_district_id: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      club_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      club_id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      contact_person_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      contact_person_number: {
        type: Sequelize.STRING(15),
        allowNull: false,
      },
      contact_person_email: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      alternate_contact_number: {
        type: Sequelize.STRING(15),
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      state: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      district: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      city: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      pincode: {
        type: Sequelize.STRING(10),
        allowNull: false,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      location_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      authorized_signatory_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      authorized_signatory_contact_number: {
        type: Sequelize.STRING(15),
        allowNull: false,
      },
      authorized_signatory_email: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      authorized_signatory_designation: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('club_details');
  }
};

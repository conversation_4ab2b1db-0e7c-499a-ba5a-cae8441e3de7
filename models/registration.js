const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Registration extends Model {
    static associate(models) {
      Registration.belongsTo(models.Tournament, {
        foreignKey: "tournamentId",
        as: "tournament",
      });
      Registration.belongsTo(models.User, {
        foreignKey: "playerId",
        as: "player",
      });
      Registration.hasMany(models.Payment, {
        foreignKey: "registrationId",
        as: "payments",
      });
      Registration.belongsTo(models.Payment, { foreignKey: "paymentId" ,as: "payment" });
    }
  }

  Registration.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tournamentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "tournament",
          key: "id",
        },
        field: "tournament_id",
      },
      regId: {
        type: DataTypes.STRING,
        unique: true,
        field: "reg_id",
      },
       referral: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      playerId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: "player_id",
      },
      registeredDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: "registered_date",
      },
      ageCategory: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "age_category",
      },
      genderCategory: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "gender_category",
      },
      tournamentTitle: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "tournament_title",
      },
      registrationRemarks: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: "registration_remarks",
      },
      paymentId: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "payment_id",
        references: {
          model: "payments",
          key: "id",
        },
      },
      attendanceMark: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: "attendance_mark",
      },
      paymentAmount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        field: "payment_amount",
      },
      paymentDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "payment_date",
      },
      status: {
        type: DataTypes.ENUM("active", "cancelled", "refunded"),
        allowNull: false,
        defaultValue: "active",
        field: "status",
      },
    },
    {
      sequelize,
      modelName: "Registration",
      tableName: "registrations",
       indexes: [
        {
          unique: true,
          fields: ["tournament_id", "player_id"],
          where: {
            status: 'active'
          },
          name: 'unique_active_registration'
        },
      ],
    }
  );

  return Registration;
};

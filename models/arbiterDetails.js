const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class ArbiterDetails extends Model {
    static associate(models) {
      ArbiterDetails.belongsTo(models.User, {
        foreignKey: "userId",
        onDelete: "CASCADE",
        as: "user",
        scope: {
          role: "arbiter",
        },
      });
    }
  }

  ArbiterDetails.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      profileUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: "profile_url",
      },

      officialId: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "official_id",
      },
      title: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "title",
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: "users",
          key: "id",
        },
        field: "user_id",
      },
      fideId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "fide_id",
      },
      aicfId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "aicf_id",
      },
      stateId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "state_id",
      },
      districtId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "district_id",
      },
      alternateContact: {
        type: DataTypes.STRING(15),
        allowNull: true,
        field: "alternate_contact",
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      district: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      pincode: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "ArbiterDetails",
      tableName: "arbiter_details",
      timestamps: true,
    }
  );

  return ArbiterDetails;
};

const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Refund extends Model {
    static associate(models) {
      Refund.belongsTo(models.Payment, {
        foreignKey: "paymentId",
        as: "payment",
      });
      Refund.belongsTo(models.Tournament, {
        foreignKey: "tournamentId",
        as: "tournament",
      });
      Refund.belongsTo(models.User, {
        foreignKey: "userId",
        as: "user",
      });
      Refund.belongsTo(models.User, {
        foreignKey: "refundInitiatedBy",
        as: "initiatedByUser",
      });
    }
  }

  Refund.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      // Our internal reference ID
      refundReference: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        field: "refund_reference",
      },
      paymentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "payments",
          key: "id",
        },
        field: "payment_id",
      },
      tournamentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "tournament",
          key: "id",
        },
        field: "tournament_id",
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: "user_id",
      },
      // Razorpay specific fields
      razorpayRefundId: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
        field: "razorpay_refund_id",
      },
      razorpayPaymentId: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "razorpay_payment_id",
      },
      // Amount in paise (to match Razorpay format)
      refundAmount: {
        type: DataTypes.DECIMAL(10, 2), 
        allowNull: false,
        field: "refund_amount",
      },
      refundCurrency: {
        type: DataTypes.STRING(3),
        allowNull: false,
        defaultValue: "INR",
        field: "refund_currency",
      },
      refundStatus: {
        type: DataTypes.ENUM("pending", "processing", "processed", "failed"),
        allowNull: false,
        defaultValue: "pending",
        field: "refund_status",
      },
      refundType: {
        type: DataTypes.ENUM("full", "partial"),
        allowNull: false,
        defaultValue: "full",
        field: "refund_type",
      },
      refundReason: {
        type: DataTypes.ENUM(
          "tournament_cancelled",
          "player_cancelled",
          "admin_cancelled",
          "duplicate_payment",
          "other"
        ),
        allowNull: false,
        defaultValue: "tournament_cancelled",
        field: "refund_reason",
      },
      refundRemarks: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: "refund_remarks",
      },
      refundInitiatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "users",
          key: "id",
        },
        field: "refund_initiated_by",
      },
      // Razorpay refund speed
      refundSpeed: {
        type: DataTypes.ENUM("normal", "optimum"),
        allowNull: true,
        defaultValue: "normal",
        field: "refund_speed",
      },
      // Store complete Razorpay refund response
      razorpayResponse: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: "razorpay_response",
      },
      // Refund dates
      refundInitiatedDate: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: "refund_initiated_date",
      },
      refundCompletedDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "refund_completed_date",
      },
    },
    {
      sequelize,
      modelName: "Refund",
      tableName: "refunds",
      timestamps: true,
      indexes: [
        {
          fields: ['refund_reference'],
          unique: true
        },
        {
          fields: ['razorpay_refund_id'],
          unique: true
        },
        {
          fields: ['razorpay_payment_id']
        },
        {
          fields: ['refund_status']
        },
        {
          fields: ['payment_id']
        },
        {
          fields: ['user_id', 'tournament_id']
        },
        {
          fields: ['refund_reason']
        }
      ]
    }
  );

  return Refund;
};
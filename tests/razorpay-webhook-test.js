/**
 * Razorpay webhook test with proper signature generation
 * Run with: node tests/razorpay-webhook-test.js
 */

const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';
const WEBHOOK_ENDPOINT = '/payment/webhook';
const WEBHOOK_SECRET = process.env.RAZORPAY_WEBHOOK_SECRET || 'your_webhook_secret_here';
const NUM_REQUESTS = 50;
const CONCURRENCY = 10;
const DELAY_BETWEEN_BATCHES_MS = 500;

// Event types to simulate
const EVENT_TYPES = [
  'payment.authorized',
  'payment.captured',
  'refund.created',
  'refund.processed'
];

// Create a valid Razorpay webhook payload
const createWebhookPayload = (index, eventType) => {
  const timestamp = Math.floor(Date.now() / 1000);
  const uniqueId = `${timestamp}_${index}`;
  
  // Base structure for all events
  const payload = {
    entity: 'event',
    account_id: 'acc_test123456',
    event: eventType,
    contains: ['payment'],
    created_at: timestamp
  };
  
  // Add event-specific data
  switch (eventType) {
    case 'payment.authorized':
    case 'payment.captured':
      payload.payload = {
        payment: {
          entity: {
            id: `pay_test_${uniqueId}`,
            amount: 50000, // 500 INR in paise
            currency: 'INR',
            status: eventType === 'payment.captured' ? 'captured' : 'authorized',
            order_id: `order_test_${uniqueId}`,
            method: 'card',
            created_at: timestamp
          }
        }
      };
      break;
      
    case 'refund.created':
    case 'refund.processed':
      payload.payload = {
        refund: {
          entity: {
            id: `rfnd_test_${uniqueId}`,
            payment_id: `pay_test_${timestamp - 3600}_${index}`, // Payment from an hour ago
            amount: 50000,
            currency: 'INR',
            status: eventType === 'refund.processed' ? 'processed' : 'created',
            speed_processed: 'normal',
            created_at: timestamp
          }
        }
      };
      break;
  }
  
  return payload;
};

// Generate a valid Razorpay signature
// IMPORTANT: Razorpay expects the raw JSON string, not a re-stringified object
const generateSignature = (payload) => {
  // Convert payload to string exactly as it will be sent
  const payloadString = JSON.stringify(payload);
  
  // Generate HMAC SHA256 signature
  return crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(payloadString)
    .digest('hex');
};

// Send a batch of requests
async function sendBatch(startIndex, batchSize) {
  console.log(`Sending batch of ${batchSize} requests starting at index ${startIndex}`);
  
  const requests = [];
  for (let i = 0; i < batchSize; i++) {
    const index = startIndex + i;
    // Randomly select an event type
    const eventType = EVENT_TYPES[Math.floor(Math.random() * EVENT_TYPES.length)];
    const payload = createWebhookPayload(index, eventType);
    
    // Generate signature from the exact payload string that will be sent
    const payloadString = JSON.stringify(payload);
    const signature = crypto
      .createHmac('sha256', WEBHOOK_SECRET)
      .update(payloadString)
      .digest('hex');
    
    requests.push(
      axios.post(`${API_URL}${WEBHOOK_ENDPOINT}`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Razorpay-Signature': signature,
          'X-Razorpay-Event-Id': `evt_test_${Date.now()}_${index}`
        }
      })
      .then(response => {
        console.log(`Request ${index} (${eventType}) succeeded:`, response.status);
        return { success: true, index, eventType };
      })
      .catch(error => {
        console.error(`Request ${index} (${eventType}) failed:`, error.message);
        if (error.response) {
          console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        }
        return { success: false, index, eventType, error: error.message };
      })
    );
  }
  
  return Promise.all(requests);
}

// Run the test
async function runTest() {
  console.log(`🚀 Starting Razorpay webhook test - ${NUM_REQUESTS} requests with concurrency of ${CONCURRENCY}`);
  console.log(`Target: ${API_URL}${WEBHOOK_ENDPOINT}`);
  console.log(`Using webhook secret: ${WEBHOOK_SECRET.substring(0, 3)}...${WEBHOOK_SECRET.substring(WEBHOOK_SECRET.length - 3)}`);
  
  // Test a single webhook first to verify signature works
  console.log('Testing a single webhook first to verify signature...');
  const testPayload = createWebhookPayload(0, 'payment.captured');
  const testPayloadString = JSON.stringify(testPayload);
  const testSignature = crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(testPayloadString)
    .digest('hex');
  
  try {
    const testResponse = await axios.post(`${API_URL}${WEBHOOK_ENDPOINT}`, testPayload, {
      headers: {
        'Content-Type': 'application/json',
        'X-Razorpay-Signature': testSignature,
        'X-Razorpay-Event-Id': `evt_test_${Date.now()}_0`
      }
    });
    console.log('✅ Single webhook test succeeded:', testResponse.status);
    console.log('Proceeding with load test...');
  } catch (error) {
    console.error('❌ Single webhook test failed:', error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Data:`, error.response.data);
    }
    console.error('Please check your webhook secret and endpoint before continuing.');
    console.error('Aborting test.');
    return;
  }
  
  const results = {
    success: 0,
    failed: 0,
    byEventType: {},
    errors: {}
  };
  
  // Initialize event type counters
  EVENT_TYPES.forEach(type => {
    results.byEventType[type] = { success: 0, failed: 0 };
  });
  
  // Send requests in batches
  for (let i = 0; i < NUM_REQUESTS; i += CONCURRENCY) {
    const batchSize = Math.min(CONCURRENCY, NUM_REQUESTS - i);
    const batchResults = await sendBatch(i, batchSize);
    
    // Process results
    batchResults.forEach(result => {
      if (result.success) {
        results.success++;
        results.byEventType[result.eventType].success++;
      } else {
        results.failed++;
        results.byEventType[result.eventType].failed++;
        results.errors[result.error] = (results.errors[result.error] || 0) + 1;
      }
    });
    
    // Add delay between batches
    if (i + CONCURRENCY < NUM_REQUESTS) {
      console.log(`Waiting ${DELAY_BETWEEN_BATCHES_MS}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES_MS));
    }
  }
  
  // Print summary
  console.log('\n📊 Test Results:');
  console.log(`Total requests: ${NUM_REQUESTS}`);
  console.log(`Successful: ${results.success}`);
  console.log(`Failed: ${results.failed}`);
  
  console.log('\nResults by event type:');
  Object.entries(results.byEventType).forEach(([eventType, counts]) => {
    console.log(`- ${eventType}: ${counts.success} successful, ${counts.failed} failed`);
  });
  
  if (results.failed > 0) {
    console.log('\nError breakdown:');
    Object.entries(results.errors).forEach(([error, count]) => {
      console.log(`- ${error}: ${count} occurrences`);
    });
  }
  
  console.log('\n✅ Test completed');
}

// Run the test
runTest().catch(console.error);

/**
 * Simple webhook flood test to trigger connection timeout errors
 * Run with: node tests/webhook-flood-test.js
 */

const axios = require('axios');
require('dotenv').config();

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';
const WEBHOOK_ENDPOINT = '/payment/webhook'; // Adjust to your actual webhook endpoint
const NUM_REQUESTS = 100;
const CONCURRENCY = 20; // Number of concurrent requests
const DELAY_BETWEEN_BATCHES_MS = 500;

// Create a mock webhook payload
const createMockPayload = (index) => ({
  event: "refund.processed",
  entity: {
    id: `rfnd_test_${Date.now()}_${index}`,
    payment_id: `pay_test_${Date.now()}_${index}`,
    amount: 1000,
    currency: "INR",
    status: "processed",
    speed_processed: "normal",
    speed_requested: "normal",
    created_at: Math.floor(Date.now() / 1000)
  }
});

// Create a mock signature (in real scenarios this would be properly signed)
const createMockSignature = () => {
  return 'mock_signature_' + Math.random().toString(36).substring(2);
};

// Send a batch of requests
async function sendBatch(startIndex, batchSize) {
  console.log(`Sending batch of ${batchSize} requests starting at index ${startIndex}`);
  
  const requests = [];
  for (let i = 0; i < batchSize; i++) {
    const index = startIndex + i;
    const payload = createMockPayload(index);
    
    requests.push(
      axios.post(`${API_URL}${WEBHOOK_ENDPOINT}`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Razorpay-Signature': createMockSignature()
        }
      })
      .then(response => {
        console.log(`Request ${index} succeeded:`, response.status);
        return { success: true, index };
      })
      .catch(error => {
        console.error(`Request ${index} failed:`, error.message);
        if (error.response) {
          console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        }
        return { success: false, index, error: error.message };
      })
    );
  }
  
  return Promise.all(requests);
}

// Run the test
async function runTest() {
  console.log(`🚀 Starting webhook flood test - ${NUM_REQUESTS} requests with concurrency of ${CONCURRENCY}`);
  console.log(`Target: ${API_URL}${WEBHOOK_ENDPOINT}`);
  
  const results = {
    success: 0,
    failed: 0,
    errors: {}
  };
  
  // Send requests in batches
  for (let i = 0; i < NUM_REQUESTS; i += CONCURRENCY) {
    const batchSize = Math.min(CONCURRENCY, NUM_REQUESTS - i);
    const batchResults = await sendBatch(i, batchSize);
    
    // Process results
    batchResults.forEach(result => {
      if (result.success) {
        results.success++;
      } else {
        results.failed++;
        results.errors[result.error] = (results.errors[result.error] || 0) + 1;
      }
    });
    
    // Add delay between batches
    if (i + CONCURRENCY < NUM_REQUESTS) {
      console.log(`Waiting ${DELAY_BETWEEN_BATCHES_MS}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES_MS));
    }
  }
  
  // Print summary
  console.log('\n📊 Test Results:');
  console.log(`Total requests: ${NUM_REQUESTS}`);
  console.log(`Successful: ${results.success}`);
  console.log(`Failed: ${results.failed}`);
  
  if (results.failed > 0) {
    console.log('\nError breakdown:');
    Object.entries(results.errors).forEach(([error, count]) => {
      console.log(`- ${error}: ${count} occurrences`);
    });
  }
  
  console.log('\n✅ Test completed');
}

// Run the test
runTest().catch(console.error);
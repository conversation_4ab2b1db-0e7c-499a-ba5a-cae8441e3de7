{
  "timestamp": "2025-06-07T04:16:44.039Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.created",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "5BB0D45EE9FE62DA9C271137F0BF5A7B"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeAc4EuPaluMtA",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "fd11ba28-66a7-425a-9f70-b5992ff58435",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeAbj5c3Cjh0P7",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@payu"
      },
      "vpa": "success@payu",
      "wallet": null
    },
    "processingTime": 349
  }
}

{
  "timestamp": "2025-06-07T04:16:44.167Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.processed",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "5BB0D45EE9FE62DA9C271137F0BF5A7B"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeAc4EuPaluMtA",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "fd11ba28-66a7-425a-9f70-b5992ff58435",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeAbj5c3Cjh0P7",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@payu"
      },
      "vpa": "success@payu",
      "wallet": null
    },
    "processingTime": 508
  }
}

{
  "timestamp": "2025-06-07T04:38:21.378Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.processed",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "497D1B05563BDB5315251ED0B9CF60B0"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Bulk Registration for poonamalle-tournament (1 players)",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeAsAmf2Ird3XS",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "bulkRegistrationId": "0219521e-77cb-4689-b6af-97d72566ccac",
        "clubEmail": "<EMAIL>",
        "clubName": "Mumbai chess club ",
        "clubPhone": "**********",
        "paymentId": "38e363cb-2697-4360-aab9-b5f2ff8b8119",
        "paymentType": "bulk",
        "playerCount": "1",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "da148cc6-e338-4181-bc4a-decba1ea0e40"
      },
      "order_id": "order_QeArzrTR5PGOgs",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@razorpay"
      },
      "vpa": "success@razorpay",
      "wallet": null
    },
    "processingTime": 855
  }
}

{
  "timestamp": "2025-06-07T04:38:21.467Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.created",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "497D1B05563BDB5315251ED0B9CF60B0"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Bulk Registration for poonamalle-tournament (1 players)",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeAsAmf2Ird3XS",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "bulkRegistrationId": "0219521e-77cb-4689-b6af-97d72566ccac",
        "clubEmail": "<EMAIL>",
        "clubName": "Mumbai chess club ",
        "clubPhone": "**********",
        "paymentId": "38e363cb-2697-4360-aab9-b5f2ff8b8119",
        "paymentType": "bulk",
        "playerCount": "1",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "da148cc6-e338-4181-bc4a-decba1ea0e40"
      },
      "order_id": "order_QeArzrTR5PGOgs",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@razorpay"
      },
      "vpa": "success@razorpay",
      "wallet": null
    },
    "processingTime": 943
  }
}

{
  "timestamp": "2025-06-07T04:47:08.136Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.created",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "D56E38DECA0B1D8AD2DB77AAED6335D1"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBA1cqdPfvLtW",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "299063ae-1a25-474d-b8fb-8807369cdf0b",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeB9fgs5eUzFeM",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@pu"
      },
      "vpa": "success@pu",
      "wallet": null
    },
    "processingTime": 635
  }
}

{
  "timestamp": "2025-06-07T04:47:08.293Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.processed",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "D56E38DECA0B1D8AD2DB77AAED6335D1"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBA1cqdPfvLtW",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "299063ae-1a25-474d-b8fb-8807369cdf0b",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeB9fgs5eUzFeM",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@pu"
      },
      "vpa": "success@pu",
      "wallet": null
    },
    "processingTime": 815
  }
}

{
  "timestamp": "2025-06-07T05:12:23.659Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.created",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "544706BC35728591EF9D253213062365"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBVTo6A08k5IH",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "64ec5e68-12eb-4849-bbaf-64e40aa881f6",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBVId9KWL2Oef",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@razy"
      },
      "vpa": "success@razy",
      "wallet": null
    },
    "processingTime": 298
  }
}

{
  "timestamp": "2025-06-07T05:12:23.812Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.processed",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "544706BC35728591EF9D253213062365"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBVTo6A08k5IH",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "64ec5e68-12eb-4849-bbaf-64e40aa881f6",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBVId9KWL2Oef",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@razy"
      },
      "vpa": "success@razy",
      "wallet": null
    },
    "processingTime": 480
  }
}

{
  "timestamp": "2025-06-07T05:20:37.429Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.created",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "8F4859410E961BD3A82AEDA1F1859F14"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBhsmgcW9eiO7",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "7fa6517f-3431-4e6d-9ce4-1939e4b7df38",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBhhFRm7fC5LV",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@razorpay"
      },
      "vpa": "success@razorpay",
      "wallet": null
    },
    "processingTime": 324
  }
}

{
  "timestamp": "2025-06-07T05:20:37.706Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.processed",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "8F4859410E961BD3A82AEDA1F1859F14"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBhsmgcW9eiO7",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "7fa6517f-3431-4e6d-9ce4-1939e4b7df38",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBhhFRm7fC5LV",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@razorpay"
      },
      "vpa": "success@razorpay",
      "wallet": null
    },
    "processingTime": 513
  }
}

{
  "timestamp": "2025-06-07T05:27:16.653Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.created",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "364AC9F338702EA84BA5763C0B3ED30F"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBlB0H4m16v5K",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "1be1d420-9fae-4d41-b2e0-04e57a139325",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBl2r8hgNVJrG",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@ra"
      },
      "vpa": "success@ra",
      "wallet": null
    },
    "processingTime": 864
  }
}

{
  "timestamp": "2025-06-07T05:27:19.032Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.processed",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "364AC9F338702EA84BA5763C0B3ED30F"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBlB0H4m16v5K",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "1be1d420-9fae-4d41-b2e0-04e57a139325",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBl2r8hgNVJrG",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@ra"
      },
      "vpa": "success@ra",
      "wallet": null
    },
    "processingTime": 1221
  }
}

{
  "timestamp": "2025-06-07T05:31:03.189Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.created",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "7B14CAE5EA4E8EA26C36EFF82FF4299A"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBs8P6cAlGYuF",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "743dde6f-331d-4da7-a2ed-276ffd7e6574",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBruF9qkjoGAX",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@payu"
      },
      "vpa": "success@payu",
      "wallet": null
    },
    "processingTime": 838
  }
}

{
  "timestamp": "2025-06-07T05:31:03.412Z",
  "type": "refund_webhook",
  "data": {
    "event": "refund.processed",
    "entity": {
      "acquirer_data": {
        "rrn": "************",
        "upi_transaction_id": "7B14CAE5EA4E8EA26C36EFF82FF4299A"
      },
      "amount": 50000,
      "amount_refunded": 50000,
      "amount_transferred": 0,
      "bank": null,
      "base_amount": 50000,
      "captured": true,
      "card_id": null,
      "contact": "+91**********",
      "created_at": **********,
      "currency": "INR",
      "description": "Registration for poonamalle-tournament",
      "email": "<EMAIL>",
      "entity": "payment",
      "error_code": null,
      "error_description": null,
      "error_reason": null,
      "error_source": null,
      "error_step": null,
      "fee": 1180,
      "id": "pay_QeBs8P6cAlGYuF",
      "international": false,
      "invoice_id": null,
      "method": "upi",
      "notes": {
        "eligibleCategory": "male-OPEN",
        "paymentId": "743dde6f-331d-4da7-a2ed-276ffd7e6574",
        "playerEmail": "<EMAIL>",
        "playerName": "jagadeesh jaga",
        "playerPhone": "**********",
        "tournamentId": "b3af53c6-5a5e-43fe-882e-59e9ecf32a14",
        "tournamentTitle": "poonamalle-tournament",
        "userId": "0eb3ca60-a31a-49f8-abe5-0f804d28c780"
      },
      "order_id": "order_QeBruF9qkjoGAX",
      "refund_status": "full",
      "status": "refunded",
      "tax": 180,
      "upi": {
        "vpa": "success@payu"
      },
      "vpa": "success@payu",
      "wallet": null
    },
    "processingTime": 964
  }
}


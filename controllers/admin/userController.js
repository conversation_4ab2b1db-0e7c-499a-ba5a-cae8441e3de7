const { Op, literal } = require("sequelize");
const { sendResponse, handleError } = require("../../utils/apiResponse");
const { getAllPlayerSchema, getAllArbiterSchema } = require("../../schema/playerSchama");
const { getAllClubSchema } = require("../../schema/clubSchema");
const {
  ClubDetail,
  ArbiterDetails,
  PlayerDetail,
  User,
} = require("../../config/db").models;

const getAllClubDetails = async (req, res) => {
  const { data, success, error } = getAllClubSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      clubName = "",
      country = "",
      state = "",
      district = "",
      city = "",
    } = data;

    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { clubName, country, state, district, city };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    const AdminScopedUser = User.scope('adminAccess');
    const { rows: clubs, count } = await ClubDetail.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: { exclude: ["id", "createdAt", "updatedAt"] },
      include: [
        {
          model: AdminScopedUser,
          as: "user",
          attributes: ["email", "phoneNumber"],
          required: false,
        },
      ],
      order: [["clubName", "ASC"]],
    });

    const response = {
      clubs,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getAllArbiter = async (req, res) => {
  const { data, success, error } = getAllArbiterSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      arbiterId,
      arbiterName,
      country,
      state,
      district,
    } = data;
    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (arbiterId && !arbiterId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${arbiterId}%` } },
        { aicfId: { [Op.iLike]: `%${arbiterId}%` } },
        { stateId: { [Op.iLike]: `%${arbiterId}%` } },
        { districtId: { [Op.iLike]: `%${arbiterId}%` } },
        { officialId: { [Op.iLike]: `%${arbiterId}%` } },
      ];
    }
    let userWhereClause = {};
    if (arbiterId && arbiterId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${arbiterId}%` } };
    }
    if (arbiterName) userWhereClause.name = { [Op.iLike]: `%${arbiterName}%` };

    const AdminScopedUser = User.scope('adminAccess');
    const { rows: arbiters, count } = await ArbiterDetails.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: [
        "title",

        "fideId",
        "aicfId",
        "districtId",
        "stateId",
        "profileUrl",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: AdminScopedUser,
          where: userWhereClause,
          as: "user",
          attributes: ["cbid", "name", "email", "phoneNumber", "id"],
          required: true,
        },
      ],
    });

    const formattedArbiters = arbiters.map((arbiter) => {
      const arbiterData = arbiter.toJSON();
      if (arbiter.user) {
        return { ...arbiterData, ...arbiter.user.toJSON() };
      }
      return arbiterData;
    });
    const response = {
      arbiters: formattedArbiters,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getAllPlayer = async (req, res) => {
  const { data, success, error } = getAllPlayerSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      playerId,
      playerName,
      country,
      state,
      district,
    } = data;
    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (playerId && !playerId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${playerId}%` } },
        { aicfId: { [Op.iLike]: `%${playerId}%` } },
        { stateId: { [Op.iLike]: `%${playerId}%` } },
        { districtId: { [Op.iLike]: `%${playerId}%` } },
      ];
    }
    let userWhereClause = {};
    if (playerId && playerId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${playerId}%` } };
    }
    if (playerName) userWhereClause.name = { [Op.iLike]: `%${playerName}%` };
    const AdminScopedUser = User.scope('adminAccess');
    const { rows: players, count } = await PlayerDetail.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: [
        "playerTitle",
        "fideRating",
        "fideId",
        "aicfId",
        "districtId",
        "stateId",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: AdminScopedUser,
          where: userWhereClause,
          attributes: ["cbid", "name", "email", "phoneNumber", "id"],
          required: true,
        },
      ],
    });
    const formattedPlayers = players.map((player) => {
      const playerData = player.toJSON();
      if (player.User) {
        return { ...playerData, ...player.User.toJSON() };
      }
      return playerData;
    });
    const response = {
      players: formattedPlayers,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getRecipients = async (recipientType, searchFilters, bulkSend) => {
  if (!bulkSend) return [];

  let detailModel;
  let includeAlias;
  const userWhereClause = { role: recipientType };
  const detailsWhereClause = {};

  // Set correct detail model and handle name/ID filters
  switch (recipientType) {
    case 'player':
      detailModel = PlayerDetail;
      includeAlias = 'PlayerDetail';

      // Handle player name filter
      if (searchFilters.playerName) {
        userWhereClause.name = { [Op.iLike]: `%${searchFilters.playerName}%` };
      }

      // Handle player ID filter
      if (searchFilters.playerId) {
        if (searchFilters.playerId.toLowerCase().startsWith("cb")) {
          // CB ID goes to user table
          userWhereClause.cbid = { [Op.iLike]: `%${searchFilters.playerId}%` };
        } else {
          // Other IDs go to player details table
          detailsWhereClause[Op.or] = [
            { fideId: { [Op.iLike]: `%${searchFilters.playerId}%` } },
            { aicfId: { [Op.iLike]: `%${searchFilters.playerId}%` } },
            { stateId: { [Op.iLike]: `%${searchFilters.playerId}%` } },
            { districtId: { [Op.iLike]: `%${searchFilters.playerId}%` } }
          ];
        }
      }
      break;

    case 'club':
      detailModel = ClubDetail;
      includeAlias = 'ClubDetail';

      if (searchFilters.clubName) {
        detailsWhereClause.clubName = { [Op.iLike]: `%${searchFilters.clubName}%` };
      }
      break;

    case 'arbiter':
      detailModel = ArbiterDetails;
      includeAlias = 'ArbiterDetails';

      // Handle arbiter name filter
      if (searchFilters.arbiterName) {
        userWhereClause.name = { [Op.iLike]: `%${searchFilters.arbiterName}%` };
      }

      // Handle arbiter ID filter
      if (searchFilters.arbiterId) {
        if (searchFilters.arbiterId.toLowerCase().startsWith("cb")) {
          // CB ID goes to user table
          userWhereClause.cbid = { [Op.iLike]: `%${searchFilters.arbiterId}%` };
        } else {
          // Other IDs go to arbiter details table
          detailsWhereClause[Op.or] = [
            { fideId: { [Op.iLike]: `%${searchFilters.arbiterId}%` } },
            { aicfId: { [Op.iLike]: `%${searchFilters.arbiterId}%` } },
            { stateId: { [Op.iLike]: `%${searchFilters.arbiterId}%` } },
            { districtId: { [Op.iLike]: `%${searchFilters.arbiterId}%` } },
            { officialId: { [Op.iLike]: `%${searchFilters.arbiterId}%` } }
          ];
        }
      }
      break;

    default:
      throw new Error('Invalid recipient type');
  }

  // Add common location filters to details table
  const locationFilters = ['country', 'state', 'district', 'city'];
  locationFilters.forEach(filter => {
    if (searchFilters[filter]) {
      detailsWhereClause[filter] = { [Op.iLike]: `%${searchFilters[filter]}%` };
    }
  });

  try {
    const users = await User.findAll({
      where: userWhereClause,
      attributes: ['id', 'name', 'email', 'phoneNumber', 'cbid'],
      include: [
        {
          model: detailModel,
          // as: includeAlias,
          where: Object.keys(detailsWhereClause).length > 0 ? detailsWhereClause : undefined,
          required: Object.keys(detailsWhereClause).length > 0 // Only require join if we have detail filters
        }
      ]
    });

   

    return users;

  } catch (error) {
    console.error('Error in getRecipients:', error);
    throw new Error(`Failed to fetch recipients: ${error.message}`);
  }
};


const getAllUsers = async (req, res) => {
  try {
    const { type, page = 1, limit = 10, name, availability } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    let whereClause = { role: type };
    if (name) {
      whereClause.name = { [Op.iLike]: `%${name}%` };
    }

    let include = [];
    if (type === 'player') {
      include.push({
        model: PlayerDetail,
        attributes: ['id'],
        required: availability,
      });
    } else if (type === 'club') {
      include.push({
        model: ClubDetail,
        attributes: ['id'],
        required: availability,
      });
    } else if (type === 'arbiter') {
      include.push({
        model: ArbiterDetails,
        attributes: ['id'],
        required: availability,
      });
    }

    const { rows: users, count } = await User.scope('adminAccess').findAndCountAll({
      where: whereClause,
      attributes: ["id", "email", "phoneNumber", "role", "name", "isActive", "isAccess"],
      offset,
      limit: limitNum,
      include
    });

    const withDetail = [];
    const withoutDetail = [];

    users.forEach((user) => {
      const jsonUser = user.toJSON();
      const hasPlayerDetail =
        !!jsonUser?.PlayerDetail?.id ||
        !!jsonUser?.ClubDetail?.id ||
        !!jsonUser?.ArbiterDetail?.id;

      const formattedUser = {
        ...jsonUser,
        hasPlayerDetail,
      };

      if (hasPlayerDetail) {
        withDetail.push(formattedUser);
      } else {
        withoutDetail.push(formattedUser);
      }
    });

    let filteredUsers;
    if (availability) {
      filteredUsers = withDetail;
    } else {
      filteredUsers = withoutDetail;
    }

    return sendResponse(res, 200, {
      success: true,
      user: filteredUsers,
      pagination: {
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(count / limitNum),
        totalRecords: count,
      },
    });
  } catch (error) {
    console.error("Error in getAllUsers:", error);
    return sendResponse(res, 500, {
      success: false,
      error: { message: "Server Error" },
    });
  }
};




module.exports = {
  getAllClubDetails,
  getAllArbiter,
  getAllPlayer,
  getRecipients,
  getAllUsers,
};

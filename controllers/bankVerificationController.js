
const { 
  verifyBankAccount, 
  getBankDetailsByIFSC, 
  createContact,
  createFundAccount,
  setupClubForPayouts 
} = require("../utils/razorPay");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { bankDetailsSchema } = require("../schema/bankSchema");
const { Bankdetails, User,ClubDetail } = require("../config/db").models;
const { Op } = require('sequelize');


/**
 * Create banking details with complete Razorpay payout setup
 */
const createBankingDetails = async (req, res) => {
  try {
    const { data, success } = bankDetailsSchema.safeParse(req.body);
    const userId = req.user?.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: bankDetailsSchema.error.format(),
      });
    }

    const { AccountNumber, branchIFSCCode, bankAccountHolderName ,bankName, branchName, bankAccountType } = data;

    // Step 1: Check if user already has banking details
    const existingDetails = await Bankdetails.findOne({
      where: { clubId: userId }
    });

    if (existingDetails && existingDetails.isVerified) {
      return sendResponse(res, 400, {
        success: false,
        error: "Banking details already exist and are verified",
        message: "You can only have one verified bank account per club",
      });
    }

    // Step 2: IFSC validation
    const ifscValidation = await getBankDetailsByIFSC(branchIFSCCode);
    
    if (!ifscValidation.success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid IFSC code format",
        details: ifscValidation.message,
      });
    }

    // Step 3: Get club details for complete contact creation
    // You'll need to fetch club details from your Club model
    const clubDetails = await User.findOne({
      where: { id: userId ,isActive: true ,role: 'club'},
      attributes: ['name', 'email', 'phoneNumber'],
      include: [
        {
          model: ClubDetail,
          attributes: ['clubId'],
          required: true,
        },
      ],
    });
    
    if (!clubDetails) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club details not found",
        message: "Please complete your club profile first",
      });
    }

    // Step 4: Setup complete payout infrastructure
    const payoutSetup = await setupClubForPayouts({
      id: userId,
      name: clubDetails.name || bankAccountHolderName,
      email: clubDetails.email,
      phone: clubDetails.phoneNumber,
      clubId:clubDetails.ClubDetail.clubId,
      bankDetails: {
        accountHolderName: bankAccountHolderName,
        accountNumber: AccountNumber,
        ifscCode: branchIFSCCode,
        bankAccountType: bankAccountType,
        bankName: bankName,
        branchName: branchName,
      }
    });

    if (!payoutSetup.success) {
      console.error("❌ Payout setup failed:", payoutSetup.error);
      
      return sendResponse(res, 400, {
        success: false,
        error: "Complete payout setup failed",
        details: {
          reason: payoutSetup.error,
          step: "payout_infrastructure_setup"
        },
      });
    }

    // Step 5: Check verification status
    const isVerified = payoutSetup.verification.status === "completed" && payoutSetup.verification.success;
    const isPending = payoutSetup.verification.is_pending || payoutSetup.verification.webhook_required;

    if (isPending) {
      console.log("⚡ Verification initiated, webhook will complete the process...");
    } else if (!isVerified) {
      console.log("⏳ Verification pending, saving setup details...");
    }

    // Step 6: Create/Update banking details record with complete setup
    const bankingData = {
      ...data,
      clubId: userId,
      isVerified: isVerified,
      isLocked: isVerified,
      payoutEnabled: isVerified,
      metadata: {
        razorpay_setup: {
          contact_id: payoutSetup.contact_id,
          fund_account_id: payoutSetup.fund_account_id,
          validation_id: payoutSetup.verification.validation_id,
          verification_status: payoutSetup.verification.status,
          verification_timestamp: payoutSetup.verification.metadata?.verification_timestamp,
          amount_deposited: payoutSetup.verification.amount_deposited || 0,
          setup_date: payoutSetup.setup_date,
          payout_ready: isVerified
        },
        ifsc_validation: ifscValidation,
        club_details: {
          name: clubDetails.name,
          email: clubDetails.email,
          phone: clubDetails.phone
        },
        created_at: new Date().toISOString(),
      },
    };

    let bankingDetails;
    if (existingDetails) {
      bankingDetails = await existingDetails.update(bankingData);
    } else {
      bankingDetails = await Bankdetails.create(bankingData);
    }

   

    const responseData = {
      success: true,
      data: {
        message: isVerified
          ? "Banking details verified and payout setup completed"
          : isPending
            ? "Banking details saved and verification initiated successfully"
            : "Banking details saved, verification in progress",
        id: bankingDetails.id,
        verification_status: payoutSetup.verification.status,
        payout_ready: isVerified,
        verification_pending: isPending,
        webhook_enabled: isPending,
        setup_details: {
          contact_id: payoutSetup.contact_id,
          fund_account_id: payoutSetup.fund_account_id,
          validation_id: payoutSetup.verification.validation_id,
          amount_deposited: payoutSetup.verification.amount_deposited
            ? `₹${(payoutSetup.verification.amount_deposited / 100).toFixed(2)}`
            : "₹0.00",
          verified_at: payoutSetup.verification.metadata?.verification_timestamp,
          bank_name: ifscValidation.bank_name,
          branch_name: ifscValidation.branch
        },
      },
    };

    if (isPending) {
      responseData.data.message += ". You'll be notified automatically when verification completes.";
      responseData.data.verification_note = "Verification usually completes within 5-10 minutes. A ₹1 deposit will be made for verification and will be refunded.";
      responseData.data.next_steps = [
        "Check verification status using the verify-status endpoint",
        "You'll receive automatic updates when verification completes",
        "No need to wait - you can continue using other features"
      ];
    } else if (!isVerified) {
      responseData.data.message += ". Please wait for verification to complete.";
      responseData.data.verification_note = "Verification typically takes 5-10 minutes. You'll receive a small deposit of ₹1 for verification.";
    }

    return sendResponse(res, 201, responseData);

  } catch (error) {
    console.error("❌ Error in createBankingDetails:", error);
    handleError(res, error);
  }
};

/**
 * Get banking details with complete payout information
 */
const getBankingDetails = async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    const bankingDetails = await Bankdetails.findOne({
      where: { clubId: userId },
      attributes: [
        'id',
        'bankName',
        'AccountNumber',
        'branchIFSCCode',
        'branchName',
        'bankAccountType',
        'bankAccountHolderName',
        'isVerified',
        'isLocked',
        'payoutEnabled',
        'metadata',
        'createdAt',
        'updatedAt'
      ],
    });

    if (!bankingDetails) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banking details not found",
        message: "Please add your banking details first"
      });
    }

    // Mask account number for security
    const maskedAccountNumber = bankingDetails.AccountNumber.replace(
      /(?<=.{4})./g, 
      '*'
    );

    const razorpaySetup = bankingDetails.metadata?.razorpay_setup || {};
    const ifscValidation = bankingDetails.metadata?.ifsc_validation || {};

    return sendResponse(res, 200, {
      success: true,
      data: {
        id: bankingDetails.id,
        bankName: bankingDetails.bankName,
        AccountNumber: maskedAccountNumber,
        branchIFSCCode: bankingDetails.branchIFSCCode,
        branchName: bankingDetails.branchName,
        bankAccountType: bankingDetails.bankAccountType,
        bankAccountHolderName: bankingDetails.bankAccountHolderName,
        isVerified: bankingDetails.isVerified,
        isLocked: bankingDetails.isLocked,
        payoutEnabled: bankingDetails.payoutEnabled || false,
        
        // Enhanced verification details
        verification_details: {
          status: razorpaySetup.verification_status || 'pending',
          contact_id: razorpaySetup.contact_id,
          fund_account_id: razorpaySetup.fund_account_id,
          validation_id: razorpaySetup.validation_id,
          amount_deposited: razorpaySetup.amount_deposited 
            ? `₹${(razorpaySetup.amount_deposited / 100).toFixed(2)}` 
            : "₹0.00",
          verified_at: razorpaySetup.verification_timestamp,
          payout_ready: razorpaySetup.payout_ready || false
        },
        
        // Bank details
        bank_info: {
          bank_name: ifscValidation.bank_name,
          branch: ifscValidation.branch,
          city: ifscValidation.city,
          state: ifscValidation.state,
          rtgs: ifscValidation.rtgs,
          neft: ifscValidation.neft,
          imps: ifscValidation.imps
        },
        
        createdAt: bankingDetails.createdAt,
        updatedAt: bankingDetails.updatedAt
      },
    });

  } catch (error) {
    console.error("❌ Error in getBankingDetails:", error);
    handleError(res, error);
  }
};

/**
 * Check verification status of existing banking details
 */
const checkVerificationStatus = async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    const bankingDetails = await Bankdetails.findOne({
      where: { clubId: userId },
    });

    if (!bankingDetails) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banking details not found",
      });
    }

    const razorpaySetup = bankingDetails.metadata?.razorpay_setup || {};

    if (!razorpaySetup.validation_id) {
      return sendResponse(res, 400, {
        success: false,
        error: "No verification found",
        message: "Please initiate bank verification first"
      });
    }

    // Check current verification status from Razorpay
    const { fetchValidationDetails } = require("../utils/razorPay");
    const validationStatus = await fetchValidationDetails(razorpaySetup.validation_id);

    if (!validationStatus.success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Failed to check verification status",
        details: validationStatus.error
      });
    }

    const validation = validationStatus.validation;
    const isVerified = validation.status === "completed";

    // Update local database if status changed
    if (isVerified && !bankingDetails.isVerified) {
      await bankingDetails.update({
        isVerified: true,
        isLocked: true,
        payoutEnabled: true,
        metadata: {
          ...bankingDetails.metadata,
          razorpay_setup: {
            ...razorpaySetup,
            verification_status: validation.status,
            payout_ready: true,
            last_status_check: new Date().toISOString()
          }
        }
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: {
        verification_status: validation.status,
        is_verified: isVerified,
        payout_ready: isVerified,
        amount_deposited: validation.formattedAmount,
        validation_details: {
          id: validation.id,
          fund_account_id: validation.fund_account.id,
          results: validation.results,
          created_at: validation.created_at,
          updated_at: validation.updated_at
        }
      }
    });

  } catch (error) {
    console.error("❌ Error in checkVerificationStatus:", error);
    handleError(res, error);
  }
};

/**
 * Enable/disable payouts for verified accounts
 */
const togglePayoutStatus = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { enable } = req.body;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    const bankingDetails = await Bankdetails.findOne({
      where: { clubId: userId },
    });

    if (!bankingDetails) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banking details not found",
      });
    }

    if (!bankingDetails.isVerified) {
      return sendResponse(res, 400, {
        success: false,
        error: "Cannot enable payouts for unverified account",
        message: "Please verify your bank account first"
      });
    }

    await bankingDetails.update({
      payoutEnabled: enable,
      metadata: {
        ...bankingDetails.metadata,
        payout_status_updated: new Date().toISOString(),
        payout_enabled_by_user: enable
      }
    });

    return sendResponse(res, 200, {
      success: true,
      data: {
        message: `Payouts ${enable ? 'enabled' : 'disabled'} successfully`,
        payout_enabled: enable
      }
    });

  } catch (error) {
    console.error("❌ Error in togglePayoutStatus:", error);
    handleError(res, error);
  }
};



/**
 * Get user-friendly error messages
 */
const getUserFriendlyError = (razorpayError) => {
  const errorMappings = {
    'ACCOUNT_DOES_NOT_EXIST': 'Bank account does not exist. Please check account number.',
    'INVALID_ACCOUNT_NUMBER': 'Invalid account number format.',
    'INVALID_IFSC': 'Invalid IFSC code.',
    'NAME_MISMATCH': 'Account holder name does not match bank records.',
    'BANK_ACCOUNT_VERIFICATION_FAILED': 'Bank verification failed. Please check your details.',
    'INSUFFICIENT_BALANCE': 'Insufficient balance in Razorpay account for verification.',
    'VALIDATION_TIMEOUT': 'Verification is taking longer than expected. Please try again.',
  };
  return errorMappings[razorpayError] || 'Bank verification failed. Please check your details.';
};

module.exports = {
  createBankingDetails,
  getBankingDetails,
  checkVerificationStatus,
  togglePayoutStatus,
  getUserFriendlyError
}; 

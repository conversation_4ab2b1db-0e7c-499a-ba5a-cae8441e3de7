const {
  ClubDetail,
  Bankdetails,
  Registration,
  User,
  PlayerDetail,
  BulkRegistration,
  Payment,
  Tournament,
  InviteRequest,
} = require("../config/db").models;
const {
  playerDetailSchema,
  getAllPlayerSchema,
  updatePlayerSchema,
} = require("../schema/playerSchama");
const { Op, Sequelize, where } = require("sequelize");
const { clubDetailSchema, getAllClubSchema } = require("../schema/clubSchema");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { bankDetailsSchema } = require("../schema/bankSchema");

const playerDetail = require("../models/playerDetail");
const emailService = require("../utils/mailer/emailService");
const { z } = require("zod");
const { config } = require("../config/config");
const { deleteFromS3 } = require("../utils/s3");
const {
  exportToExcel,

  generateTournamentReceipt,
  exportToExcelXMLForSwiss,
} = require("../utils/report-generation");
const { sequelize } = require("../config/db");

const clubPaymentSchema = z.object({
  tournamentTitle: z.string().optional(),
  transactionId: z.string().optional(),
  paymentType: z.enum(["player", "club", "all"]).optional().default("all"),
});

const TournamentRegPlayers = async (req, res) => {
  try {
    const { ageCategory, genderCategory, tournamentTitle } = req.query;
    console.log(ageCategory, genderCategory, tournamentTitle);

    let whereClause = {};
    if (ageCategory) whereClause.ageCategory = ageCategory;
    if (genderCategory) whereClause.genderCategory = genderCategory;
    if (tournamentTitle) whereClause.tournamentTitle = tournamentTitle;

    const { rows: details, count } = await Registration.findAndCountAll({
      where: whereClause,
      attributes: {
        exclude: [
          "createdAt",
          "updatedAt",
          "id",
          "tournamentId",
          "clubId",
          "playerId",
          "paymentId",
          "attendanceMark",
          "regId",
        ],
      },
      include: [
        {
          model: User,
          as: "player",
          attributes: ["id", "name", "email", "phoneNumber"],
          include: [
            {
              model: PlayerDetail,
              attributes: {
                exclude: [
                  "createdAt",
                  "updatedAt",
                  "id",
                  "userId",
                  "clubId",
                  "profileUrl",
                  "termsAndConditions",
                ],
              },
            },
          ],
        },
      ],
    });

    console.log(count);

    if (count === 0) {
      return sendResponse(res, 201, {
        success: false,
        error: "No player details found.",
      });
    }

    const plainDetails = details.map((registration) => {
      const plain = registration.toJSON();
      const player = plain.player || {};
      const playerDetail = player.PlayerDetail || {};

      return {
        registeredDate: plain.registeredDate || "",
        ageCategory: plain.ageCategory || "",
        genderCategory: plain.genderCategory || "",
        // tournamentTitle: plain.tournamentTitle || '',
        registrationRemarks: plain.registrationRemarks || "",
        paymentAmount: plain.paymentAmount || "",
        paymentDate: plain.paymentDate || "",

        playerName: player.name || "",
        playerEmail: player.email || "",
        playerPhone: player.phoneNumber || "",

        playerTitle: playerDetail.playerTitle || "",
        DOB: playerDetail.dob || "",
        Gender: playerDetail.gender || "",
        parentGuardianName: playerDetail.parentGuardianName || "",
        emergencyContact: playerDetail.emergencyContact || "",
        alternateContact: playerDetail.alternateContact || "",
        fideRating: playerDetail.fideRating || "",
        fideId: playerDetail.fideId || "",
        aicfId: playerDetail.aicfId || "",
        stateId: playerDetail.stateId || "",
        districtId: playerDetail.districtId || "",
        association: playerDetail.association || "",
        club: playerDetail.club || "",
        country: playerDetail.country || "",
        state: playerDetail.state || "",
        district: playerDetail.district || "",
        city: playerDetail.city || "",
        pincode: playerDetail.pincode || "",
        address: playerDetail.address || "",
      };
    });

    const result = await exportToExcel({
      data: plainDetails,
      sheetName: "Players_details",
      title: `Tournament Players Report${
        tournamentTitle ? ` - ${tournamentTitle}` : ""
      }`,
      reportType: "Tournament Registration",
    });

    if (!result.success) {
      return sendResponse(res, 500, {
        success: false,
        error: result.error,
      });
    }

    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=tournament_players_report${
        tournamentTitle ? `_${tournamentTitle.replace(/\s+/g, "_")}` : ""
      }.xlsx`
    );

    return res.send(result.buffer);
  } catch (error) {
    handleError(res, error);
  }
};

const PublicPlayerReport = async (req, res) => {
  const { data, success, error } = getAllPlayerSchema.safeParse(req.query);
  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const { city, playerId, playerName, country, state, district } = data;

    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (playerId && !playerId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${playerId}%` } },
        { aicfId: { [Op.iLike]: `%${playerId}%` } },
        { stateId: { [Op.iLike]: `%${playerId}%` } },
        { districtId: { [Op.iLike]: `%${playerId}%` } },
      ];
    }
    let userWhereClause = {};
    if (playerId && playerId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${playerId}%` } };
    }
    if (playerName) userWhereClause.name = { [Op.iLike]: `%${playerName}%` };

    const { rows: details } = await PlayerDetail.findAndCountAll({
      where: whereClause,
      attributes: {
        exclude: [
          "createdAt",
          "updatedAt",
          "id",
          "profileUrl",
          "userId",
          "clubId",
          "termsAndConditions",
        ],
      },
      include: [
        {
          model: User,
          attributes: ["id", "name", "email", "phoneNumber"],
          required: false,
        },
      ],
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }

    const plainDetails = details.map((player) => {
      const plain = player.toJSON();
      const flattened = {
        ...plain,
        playerName: plain.User?.name || "",
        playerEmail: plain.User?.email || "",
        playerPhone: plain.User?.phoneNumber || "",
      };
      delete flattened.User;
      return flattened;
    });

    const result = await exportToExcel({
      data: plainDetails,
      sheetName: "Player_Public_details",
      title: "Player List Report",
      reportType: "Registered Players",
    });

    if (!result.success) {
      return sendResponse(res, 500, {
        success: false,
        error: result.error,
      });
    }

    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=player_list_report${
        playerName ? `_${playerName.replace(/\s+/g, "_")}` : ""
      }.xlsx`
    );

    return res.send(result.buffer);
  } catch (e) {
    handleError(res, e);
    return;
  }
};

const PaymentReport = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, errors } = clubPaymentSchema.safeParse(req.query);

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: `Invalid query parameters: ${errors}`,
      });
    }

    const { page, tournamentTitle, transactionId, paymentType } = data;

    // Build where conditions - updated to use correct status values from your model
    const whereQuery = {
      paymentStatus: ["captured", "refunded", "failed"], // Based on your ENUM: "created", "pending", "authorized", "captured", "failed", "refunded"
    };

    if (transactionId) whereQuery.paymentTransactionId = transactionId;

    // Handle different payment types if specified
    if (paymentType === "player") {
      whereQuery.paymentType = "player";
    } else if (paymentType === "club") {
      whereQuery.paymentType = "club";
    }
    // If no paymentType specified, get both

    // Create a more efficient query by joining first
    const { rows: payments, count: total } = await Payment.findAndCountAll({
      where: whereQuery,
      attributes: [
        "id",
        "paymentTransactionId",
        "paymentAmount",
        "paymentCurrency",
        "paymentStatus",
        "paymentRemarks",
        "userId",
        "paymentType",
        "paymentDate",
        "paymentMethod",
        "razorpayResponse",
        "razorpayPaymentId",
        "razorpayOrderId",
      ],
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: ["title", "clubId"],
          where: tournamentTitle ? { title: tournamentTitle } : {},
          required: tournamentTitle ? true : false,
        },
        {
          model: User,
          as: "user",
          attributes: ["cbid", "name", "email", "phoneNumber"],
          include: [
            {
              model: ClubDetail,
              attributes: ["clubId", "clubName"],
              required: false,
            },
          ],
        },
        {
          model: Registration,
          as: "registration",
          attributes: ["regId"],
          required: false,
        },
        {
          model: BulkRegistration,
          as: "bulkRegistration",
          attributes: ["playersCount", "totalAmount"],
          required: false,
        },
      ],
      order: [["paymentDate", "DESC"]], // Add ordering by payment date
      // Remove pagination here if you want all records for Excel export
      // limit: 50, // Add if you want to limit results
      // offset: (page - 1) * 50,
    });

    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          payments: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }

    // Filter payments by club ownership
    const filteredPayments = payments.filter(
      (payment) => payment.tournament?.clubId === userId
    );

    // Transform the data to include clear payment type info
    const formattedPayments = filteredPayments.map((payment) => {
      // Access the raw data properly
      const paymentData = payment.get({ plain: true });

      const basePayment = {
        paymentTransactionId: paymentData.paymentTransactionId,
        paymentAmount: paymentData.paymentAmount
          ? paymentData.paymentAmount
          : "0.00", // Convert from paise
        paymentCurrency: paymentData.paymentCurrency || "INR",
        paymentStatus: paymentData.paymentStatus,
        paymentMode: paymentData.paymentMethod || "N/A",
        paymentDate: paymentData.paymentDate
          ? new Date(paymentData.paymentDate).toLocaleDateString()
          : "N/A",
        paymentRemarks: paymentData.paymentRemarks || "",
        tournamentTitle: paymentData.tournament?.title || "N/A",
        paymentType: paymentData.paymentType || "player",
        cbid: paymentData.user?.cbid || "N/A",
        razorpayPaymentId: paymentData.razorpayPaymentId || "N/A",
        razorpayOrderId: paymentData.razorpayOrderId || "N/A",
        registrationId: paymentData.registration?.regId || "N/A",
      };

      // Add specific details based on payment type
      if (paymentData.paymentType === "club") {
        return {
          ...basePayment,
          playersCount: paymentData.bulkRegistration?.playersCount || 0,
          totalAmount: paymentData.bulkRegistration?.totalAmount
            ? paymentData.bulkRegistration.totalAmount
            : basePayment.paymentAmount,
          registrationType: "Bulk Registration",
          clubId: paymentData.user?.ClubDetail?.clubId || "N/A",
          clubName:
            paymentData.user?.ClubDetail?.clubName ||
            paymentData.user?.name ||
            "Unknown Club",
          playerName: paymentData.user?.name || "N/A", // Club contact person name
        };
      } else {
        // For player payments, try to get player name from various sources
        let playerName = "N/A";

        // Try to get from razorpay response
        if (
          paymentData.razorpayResponse &&
          paymentData.razorpayResponse.notes
        ) {
          playerName =
            paymentData.razorpayResponse.notes.playerName ||
            paymentData.user?.name ||
            "N/A";
        } else {
          playerName = paymentData.user?.name || "N/A";
        }

        return {
          ...basePayment,
          playerName: playerName,
          registrationType: "Individual Registration",
          playerEmail: paymentData.user?.email || "N/A",
          playerPhone: paymentData.user?.phoneNumber || "N/A",
        };
      }
    });

    // Export to Excel
    const result = await exportToExcel({
      data: formattedPayments,
      sheetName: "Payment_Details",
      title: `Payment Report${tournamentTitle ? ` - ${tournamentTitle}` : ""}`,
      reportType: "Tournament Payments",
    });

    if (!result.success) {
      return sendResponse(res, 500, {
        success: false,
        error: result.error,
      });
    }

    // Set response headers for Excel download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=payment_report${
        tournamentTitle ? `_${tournamentTitle.replace(/\s+/g, "_")}` : ""
      }_${new Date().toISOString().split("T")[0]}.xlsx`
    );

    return res.send(result.buffer);
  } catch (error) {
    handleError(res, error);
    return;
  }
};

const clubPlayers = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid club ID",
      });
    }

    const { rows: details, count } = await ClubDetail.findAndCountAll({
      where: { id: id },
      attributes: ["clubName"],
      include: [
        {
          model: PlayerDetail,
          as: "players",
          attributes: {
            exclude: [
              "createdAt",
              "updatedAt",
              "id",
              "userId",
              "clubId",
              "profileUrl",
              "termsAndConditions",
              "myFiles",
              "friendIds",
            ],
          },
          include: [
            {
              model: User,
              attributes: ["name", "email", "phoneNumber"],
              required: false,
            },
          ],
        },
      ],
    });

    if (count === 0) {
      return sendResponse(res, 201, {
        success: false,
        error: "No player details found.",
      });
    }

    const plain = details[0].toJSON();
    const plainDetails = (plain.players || []).map((player) => ({
      playerTitle: player.playerTitle,
      playerName: player.User.name,
      playerEmail: player.User.email,
      playerPhone: player.User.phoneNumber,
      dob: player.dob,
      gender: player.gender,
      parentGuardianName: player.parentGuardianName,
      emergencyContact: player.emergencyContact,
      alternateContact: player.alternateContact,
      fideRating: player.fideRating,
      fideId: player.fideId,
      aicfId: player.aicfId,
      stateId: player.stateId,
      districtId: player.districtId,
      association: player.association,
      club: player.club,
      country: player.country,
      state: player.state,
      district: player.district,
      city: player.city,
      pincode: player.pincode,
      address: player.address,
      // clubName: plain.clubName,
    }));

    const result = await exportToExcel({
      data: plainDetails,
      sheetName: "Club_Players_details",
      title: `Club Players Report${
        plain.clubName ? ` - ${plain.clubName}` : ""
      }`,
      reportType: "Club Players",
    });

    if (!result.success) {
      return sendResponse(res, 500, {
        success: false,
        error: result.error,
      });
    }

    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=club_players_report${
        plain.clubName ? `_${plain.clubName.replace(/\s+/g, "_")}` : ""
      }.xlsx`
    );

    return res.send(result.buffer);
  } catch (error) {
    handleError(res, error);
  }
};

const TournamentRegPlayersForSwiss = async (req, res) => {
  try {
    const { tournamentTitle } = req.query;

    let whereClause = {};
    if (tournamentTitle) whereClause.tournamentTitle = tournamentTitle;

    const { rows: details, count } = await Registration.findAndCountAll({
      where: whereClause,
      attributes: {
        exclude: [
          "createdAt",
          "updatedAt",
          "id",
          "tournamentId",
          "clubId",
          "playerId",
          "paymentId",
          "attendanceMark",
          "regId",
        ],
      },
      include: [
        {
          model: User,
          as: "player",
          attributes: ["id", "name", "email", "cbid", "phoneNumber"],
          include: [
            {
              model: PlayerDetail,
              attributes: {
                exclude: [
                  "createdAt",
                  "updatedAt",
                  "id",
                  "userId",
                  "clubId",
                  "profileUrl",
                  "termsAndConditions",
                ],
              },
            },
          ],
        },
      ],
    });

    if (count === 0) {
      return sendResponse(res, 201, {
        success: false,
        error: "No player details found.",
      });
    }

    // Transform data to match FIDE tournament format
    const plainDetails = details.map((registration, index) => {
      const plain = registration.toJSON();
      const player = plain.player || {};
      const playerDetail = player.PlayerDetail || {};
      const cbid = player.cbid || "";
      const phone = player.phoneNumber || "";
      const ageCategory = plain.ageCategory || "";
      const type = plain.genderCategory || "";
      const title = playerDetail.playerTitle || "";
      const fideRating = playerDetail.fideRating || "";
      const fideId = playerDetail.fideId || "";
      const club = playerDetail.club || "";
      const country = playerDetail.country || "IND";

      // Extract first and last name from full name
      const fullName = player.name || "";
      const nameParts = fullName.trim().split(" ");
      const lastName = nameParts[0] || "";
      const firstName =
        nameParts.length > 1 ? nameParts.slice(1).join(" ") : "";

      // Format birth year from DOB
      const birthYear = playerDetail.dob
        ? new Date(playerDetail.dob).getFullYear()
        : "";

      const sex = playerDetail.gender || "";

      return {
        "Last Name": lastName,
        "First Name": firstName,
        Title: title || "",
        "Rtg.Nat": fideRating || "",
        "Rtg.Int": fideRating || "",
        Birth: birthYear,
        Sex: sex,
        Fed: country || "IND",
        "FIDE No": fideId || "",
        Club: club || "",
        Group: ageCategory || "",
        Type: type || "",
        "ID no": cbid || phone,
      };
    });

    const result = await exportToExcelXMLForSwiss({
      data: plainDetails,
    });

    if (!result.success) {
      return sendResponse(res, 500, {
        success: false,
        error: result.error,
      });
    }

    res.setHeader("Content-Type", "application/xml");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=tournament_players_fide_format${
        tournamentTitle ? `_${tournamentTitle.replace(/\s+/g, "_")}` : ""
      }.xml`
    );

    return res.send(result.buffer);
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  TournamentRegPlayers,
  PublicPlayerReport,
  PaymentReport,
  clubPlayers,
  TournamentRegPlayersForSwiss,
};

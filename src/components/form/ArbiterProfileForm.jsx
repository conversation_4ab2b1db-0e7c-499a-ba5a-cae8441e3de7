import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON>pography,
  <PERSON>rid,
  Box,
  Avatar,
  TextField,
} from "@mui/material";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useWatch } from "react-hook-form";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate } from "react-router-dom";
import FormTextField from "../../components/form/FormTextField";
import FormPhoneInput from "../../components/form/FormPhoneInput";
import FormAutocomplete from "../../components/form/FormAutocomplete";
import FormSelectField from "./FormSelectField";
import { Edit } from "@mui/icons-material";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import { arbiterDetailSchema } from "../../schema/zod";

import ProfileImageUpload from "../common/ProfileImageUpload";

const ArbiterProfileForm = ({ edit }) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    getValues,
    reset,
  } = useForm({
    resolver: zodResolver(arbiterDetailSchema),
    defaultValues: {
      title: "",
      firstName: "",
      lastName: "",
      alternateContact: "",

      aicfId: "",
      // officialId: "",
      phoneNumber: "",
      club: "",
      state: "",
      city: "",
      otp: "",
      pincode: "",
      fideId: "",
      stateId: "",
      country: "",
      district: "",
      countryCode: "",
      stateCode: "",
    },
    reValidateMode: "onChange",
  });

  // Watch for changes to country and state codes for dependent dropdowns
  const countryCode = watch("countryCode");
  const stateCode = watch("stateCode");

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [prefetch, setPrefetch] = useState({});
  const [originalProfileData, setOriginalProfileData] = useState({});
  const toast = UseToast();
  const { user } = UseGlobalContext();
  const navigate = useNavigate();
  // const { geoInfo } = useUserGeoInfo();

  const phoneValue = useWatch({ control, name: "phoneNumber" });
  const [originalPhone, setOriginalPhone] = useState(""); // set this from props or user data
  const [phoneChanged, setPhoneChanged] = useState(false);

  const [otpSent, setOtpSent] = useState(false);
  const [otpLoading, setOtpLoading] = useState(false);
  const [otp, setOtp] = useState("");

  // Watch phone number for changes
  const phoneNumber = watch("phoneNumber");
  useEffect(() => {
    if (edit && !originalPhone) {
      setOriginalPhone(phoneValue || ""); // capture initial phone number
    }
  }, [edit, phoneValue, originalPhone]);

  // 2. Track if phone is changed
  useEffect(() => {
    if (!edit) {
      setPhoneChanged(false);
      setOriginalPhone("");
    } else {
      setPhoneChanged(phoneValue !== originalPhone && phoneValue !== "");
    }
  }, [phoneValue, originalPhone, edit]);

  // const phoneChanged = true;

  // Load saved draft from localStorage on initial render
  useEffect(() => {
    // Check for saved draft in localStorage
    const savedDraft = localStorage.getItem("arbiterProfileDraft");
    if (savedDraft && !edit) {
      try {
        const draftData = JSON.parse(savedDraft);
        // Set form values from draft
        Object.keys(draftData).forEach((key) => {
          setValue(key, draftData[key]);
        });
        toast.info("Draft loaded successfully");
      } catch (error) {
        console.error("Error loading draft:", error);
      }
    }
  }, [edit]);

  // Load countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await Client.get("/location/countries");
        if (!response.data.success) {
          console.error("Failed to fetch countries");
          return;
        }
        setCountries(response.data.data);
      } catch (error) {
        console.error("Error fetching countries:", error);
      }
    };

    fetchCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    // Get current country value
    const country = getValues("country");

    if (country) {
      const countryObj = countries.find((c) => c.name === country);
      if (countryObj) {
        const fetchStates = async () => {
          try {
            const response = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              {
                params: { country: countryObj.isoCode },
              }
            );
            if (!response.data.success) {
              toast.error("Failed to load states");
              return;
            }
            setStates(response.data.data);

            // Only reset dependent fields if not in edit mode or if country has changed
            if (!edit || !getValues("state")) {
              setValue("state", "");
              setValue("stateCode", "");
              setValue("district", "");
              setValue("city", "");
            }
          } catch (error) {
            console.error("Error fetching states:", error);
            toast.error("Failed to load states. Please try again.");
          }
        };
        fetchStates();
      }
    } else {
      setStates([]);
      // Always reset when country is cleared
      setValue("state", "");
      setValue("stateCode", "");
      setValue("district", "");
      setValue("city", "");
    }
  }, [countries, setValue, edit, getValues("country")]);

  useEffect(() => {
    if (getValues("country") === "India" && getValues("state")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      const stateObj = states.find((s) => s.name === getValues("state"));
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);

          // Only reset district if not in edit mode or if state has changed
          if (!edit || !getValues("district")) {
            setValue("district", "");
          }
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
    }
  }, [getValues("state"), countries, states, setValue, edit]);

  // Load cities when state changes
  useEffect(() => {
    // Get current country and state values
    const country = getValues("country");
    const state = getValues("state");
    const countryCode = getValues("countryCode");
    const stateCode = getValues("stateCode");

    if (country && state && countryCode && stateCode) {
      const fetchCities = async () => {
        try {
          const response = await Client.get(
            `/location/cities/${countryCode}/${stateCode}`,
            {
              params: { country: countryCode, state: stateCode },
            }
          );
          if (!response.data.success) {
            toast.error("Failed to load cities");
            return;
          }
          setCities(response.data.data);

          // Only reset city if not in edit mode or if state has changed
          if (!edit || !getValues("city")) {
            setValue("city", "");
          }
        } catch (error) {
          console.error("Error fetching cities:", error);
          toast.error("Failed to load cities. Please try again.");
        }
      };
      fetchCities();
    } else {
      setCities([]);
      // Reset city only if state is cleared
      if (!state) {
        setValue("city", "");
      }
    }
  }, [getValues("state"), reset, setValue, edit]);

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data } = await Client.get("/user");
        const { email, phoneNumber, cbid, name } = data.data;
        setValue("phoneNumber", phoneNumber);
        setOriginalPhone(phoneNumber); // Set original phone
        const [firstName, lastName] = name && name.split(" ");
        setPrefetch({ email, phoneNumber, cbid, firstName, lastName });
        setValue("firstName", firstName);
        setOriginalProfileData({
          email,
          phoneNumber,
          cbid,
          firstName,
          lastName,
        });
        setValue("lastName", lastName);
      } catch (error) {
        console.error("Error fetching user data:", error);
        const errorMessage =
          error.response?.data?.message ||
          "An unexpected error occurred while fetching user data";
        toast.error(errorMessage);
      }
    };
    if (!edit) {
      fetchUserData();
    }
  }, []);

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!edit) return;
      try {
        // setSubmitting(true);
        const response = await Client.get("/arbiter/profile");
        if (response.status === 204) {
          toast.info("You haven't created a profile yet.");
          return;
        }
        if (!response.data.success) {
          toast.info(response.data.message);
          return;
        }
        const ArbiterDetails = response.data.data;
        const [firstName, lastName] = ArbiterDetails.user.name.split(" ");

        // Process arbiter details to handle null values
        const processedDetails = {
          ...ArbiterDetails,
          // Convert null values to empty strings or appropriate defaults
          title: ArbiterDetails.title || "",
          firstName: firstName || prefetch.firstName,
          lastName: lastName || prefetch.lastName,
          phoneNumber:
            ArbiterDetails.user?.phoneNumber || prefetch.phoneNumber || "",
          alternateContact: ArbiterDetails.alternateContact || "",

          aicfId: ArbiterDetails.aicfId || "",
          email: ArbiterDetails.user?.email || prefetch.email || "",
          officialId: ArbiterDetails.officialId || "",
          club: ArbiterDetails.club || "",
          state: ArbiterDetails.state || "",
          city: ArbiterDetails.city || "",
          pincode: ArbiterDetails.pincode || "",

          fideId: ArbiterDetails.fideId || "",
          stateId: ArbiterDetails.stateId || "",
          country: ArbiterDetails.country || "",
          district: ArbiterDetails.district || "",
        };

        // Reset with the processed details
        reset(processedDetails);
        setOriginalProfileData((prev) => ({ ...prev, ...processedDetails }));

        // Then find and set the country code
        if (ArbiterDetails.country && countries.length > 0) {
          const countryObj = countries.find(
            (c) => c.name === ArbiterDetails.country
          );
          if (countryObj) {
            setValue("countryCode", countryObj.isoCode);

            // Fetch states for this country
            const statesResponse = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              { params: { country: countryObj.isoCode } }
            );

            if (statesResponse.data.success) {
              const statesData = statesResponse.data.data;
              setStates(statesData);

              // Find and set the state code
              if (ArbiterDetails.state) {
                const stateObj = statesData.find(
                  (s) => s.name === ArbiterDetails.state
                );
                if (stateObj) {
                  setValue("stateCode", stateObj.isoCode);

                  // Fetch cities for this state
                  const citiesResponse = await Client.get(
                    `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
                    {
                      params: {
                        country: countryObj.isoCode,
                        state: stateObj.isoCode,
                      },
                    }
                  );

                  if (citiesResponse.data.success) {
                    setCities(citiesResponse.data.data);
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching arbiter profile:", error);
        let errorMessage = "Error fetching arbiter data";
        if (error.response) {
          errorMessage = error.response.data?.message || "Server error";
        } else if (error.request) {
          errorMessage = "No response from server";
        } else {
          errorMessage = error.message;
        }
        toast.error("Failed to fetch arbiter profile: " + errorMessage);
      } finally {
        // setSubmitting(false);
      }
    };

    fetchProfileData();
  }, [edit, reset, countries, setValue]);

  const handleSendOtp = async () => {
    if (!edit) return; // Only in edit mode
    if (!phoneNumber) {
      toast.error("Please enter a valid phone number");
      return;
    }

    if (phoneNumber === originalPhone) {
      setOtpSent(false);
      setOtp("");
      return;
    }

    // Validate phone number format before sending OTP
    if (!/^91[0-9]{10}$/.test(phoneNumber)) {
      toast.error("Phone number must start with 91 followed by 10 digits");
      return;
    }

    // Send OTP when phone number changes
    setOtpLoading(true);
    try {
      const response = await Client.post("/auth/send-otp", {
        phoneNumber,
        type: "verification",
        userId: user?.userId,
      });

      if (response.data.success) {
        setOtpSent(true);
        setOtp("");
        toast.success("OTP sent to new phone number");
      } else {
        toast.error(response.data.message || "Failed to send OTP");
      }
    } catch (error) {
      console.error("Error sending OTP:", error);

      if (error.response?.status === 429) {
        toast.error("wait 1 minute before sending another OTP");
        return;
      }
      toast.error(
        error.response?.data?.message || "Failed to send OTP. Try again."
      );
      if (otpSent) return;
      setOtpSent(false);
    } finally {
      setOtpLoading(false);
    }
  };

  // Helper to scroll to error field and show error toast
const onError = (errors) => {
  const firstField = Object.keys(errors)[0];
  const errorData = errors[firstField];

  const sectionElement = document.querySelector(`[name="${firstField}"]`);

  if (sectionElement) {
    const scrollOffset = 100;
    const y =
      sectionElement.getBoundingClientRect().top +
      window.pageYOffset -
      scrollOffset;
    window.scrollTo({ top: y, behavior: "smooth" });

    setTimeout(() => {
      if ("focus" in sectionElement) {
        sectionElement.focus();
      }
    }, 500);
  }

  toast.info(
    `Error in field: ${firstField} – ${errorData?.message || "Invalid input"}`
  );
};

  // Form submission handler
  const onSubmit = async (data) => {
    // Save form data to localStorage as backup
    const dataForStorage = { ...data };
    delete dataForStorage.profileImage;
    delete dataForStorage.profileImagePreview; // Don't store preview in localStorage
    localStorage.setItem("arbiterProfileDraft", JSON.stringify(dataForStorage));

    toast.info(`Processing your profile ${edit ? "update" : "creation"}...`);

    try {
      // For edit mode, only send changed fields
      if (edit) {
        const changedData = {};

        // Check each field and only include ones that have changed from original
        Object.keys(data).forEach((key) => {
          // Skip otp, phoneChanged, and preview fields from comparison
          if (["otp", "phoneChanged", "profileImagePreview"].includes(key))
            return;

          // Special handling for file fields
          if (key === "profileImage") {
            if (data.profileImage && data.profileImage instanceof File) {
              changedData.profileImage = data.profileImage;
            }
            return;
          }

          // Compare with our stored original data
          if (
            JSON.stringify(data[key]) !==
            JSON.stringify(originalProfileData[key])
          ) {
            changedData[key] = data[key];
          }
        });

        // Always include these fields for user identification
        if (prefetch && prefetch.cbid) {
          changedData.cbid = prefetch.cbid;
        }

        // Handle phone number changes specifically
        if (data.phoneNumber !== originalPhone) {
          changedData.phoneNumber = data.phoneNumber;
          changedData.phoneChanged = true;
          changedData.otp = data.otp || "";
        }
        if (
          data.firstName !== originalProfileData.firstName ||
          data.lastName !== originalProfileData.lastName
        ) {
          changedData.name = `${data.firstName} ${data.lastName}`;
        }

        // Only proceed with the API call if we have changes
        if (
          Object.keys(changedData).length === 0 &&
          !changedData.profileImage
        ) {
          toast.info("No changes detected to update.");
          return;
        }

        // Create FormData to handle file upload
        const formData = new FormData();

        // Add all changed fields to FormData
        Object.keys(changedData).forEach((key) => {
          const value = changedData[key];

          if (key === "profileImage" && value instanceof File) {
            formData.append("profileImage", value);
          } else if (typeof value === "object" && value !== null) {
            // Only stringify if it's an object (not null)
            formData.append(key, JSON.stringify(value));
          } else {
            // No quotes around strings, booleans, or numbers
            formData.append(key, String(value));
          }
        });

        // Log what's being sent (but not the actual file contents)
        const response = await Client.put("/arbiter/profile", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
        handleResponse(response);
        return;
      }

      // For create mode, send all data including image
      const formData = new FormData();

      // Add all fields to FormData except the preview
      Object.keys(data).forEach((key) => {
        if (key === "profileImagePreview") return; // Skip preview

        if (key === "profileImage" && data[key] instanceof File) {
          formData.append("profileImage", data[key]);
        } else {
          formData.append(key, data[key] || "");
        }
      });

      const response = await Client.post("/arbiter/profile", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      handleResponse(response);
    } catch (error) {
      handleError(error);
    } finally {
      // setSubmitting(false);
    }
  };
  const handleResponse = (response) => {
    if (!response.data.success) {
      if (response.data.zodErrors) {
        // Show the first Zod validation error
        const zodErrors = response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else {
        toast.error(response.data.error || "Unexpected error");
      }
      return;
    }

    toast.success(`Profile ${edit ? "updated" : "created"} successfully`);

    // Clear draft from localStorage after successful submission
    localStorage.removeItem("arbiterProfileDraft");
    sessionStorage.removeItem("new_user");

    // Reset the form
    reset();

    // Navigate back to profile page
    navigate("/dashboard/profile");
  };

  // Helper function to handle errors
  const handleError = (error) => {
    console.error(`Error ${edit ? "updating" : "creating"} profile:`, error);

    if (error.response?.data?.error?.message === "Incorrect OTP.") {
      toast.error("Incorrect OTP. Please try again.");
    } else if (error.response?.status === 422 && error.response?.data?.data) {
      const validationErrors = error.response.data.data;
      Object.keys(validationErrors).forEach((key) => {
        if (key !== "_errors" && validationErrors[key]._errors) {
          toast.error(
            `Validation failed - ${key}: ${validationErrors[key]._errors.join(
              ", "
            )}`
          );
        }
      });
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error.message || "An error occurred");
    } else if (error.response?.data?.zodErrors) {
      // Handle Zod validation errors from the server
      const zodErrors = error.response.data.zodErrors;
      const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
      toast.error(firstZodError || "Please correct the validation errors");
    } else {
      const errorMessage =
        error.response?.data?.message ||
        `An unexpected error occurred while ${
          edit ? "updating" : "creating"
        } the profile.`;
      toast.error(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit,onError)}>
      <Grid
        container
        spacing={3}
        sx={{
          ".MuiFormControl-root": { mt: "4px !important" },
          ".MuiInputBase-input": { py: 1.5 },
          ".MuiAutocomplete-input": { p: "4px !important" },
          ".MuiGrid-root.MuiGrid-item": { pt: "0px !important" },
          position: "relative",
        }}
      >
        {/* CBID and Profile Image Row */}
        <Grid container item xs={12} md={12} sx={{ mb: 2 }}>
          <Grid item xs={6} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              CBID: {prefetch.cbid || ""}
            </Typography>
            {/* <Grid item xs={12} md={6}>
              <FormTextField
                name="officialId"
                control={control}
                title="Official ID"
                placeholder="Enter Official ID"
                specialCharAllowed={true}
              />
            </Grid> */}
            <Grid item xs={12} md={6}>
              <FormSelectField
                name="title"
                control={control}
                options={[
                  { value: "Untitled", label: "Untitled" },
                  { value: "SA", label: "State Arbiter" },
                  { value: "SNA", label: "Senior National Arbiter" },
                  { value: "FA", label: "Fide Arbiter" },
                  { value: "IA", label: "International Arbiter" },
                ]}
                title="Arbiter Title"
                placeholder="Select Title"
                required
                rules={{ required: "Arbiter Title is required" }}
              />
            </Grid>
          </Grid>

          <Grid
            item
            xs={6}
            md={6}
            sx={{ display: "flex", justifyContent: "end" }}
          >
            <ProfileImageUpload
              control={control}
              setValue={setValue}
              watch={watch}
            />
          </Grid>
        </Grid>

        {/* Basic Details Fields */}
        <Grid item xs={12} md={6} sx={{ display: "flex", gap: 2 }}>
          <Grid item xs={6}>
            <FormTextField
              name="firstName"
              control={control}
              title="First Name"
              placeholder="Enter First Name"
              required
              disabled={!edit}
            />
          </Grid>
          <Grid item xs={6}>
            <FormTextField
              name="lastName"
              control={control}
              title="Last Name"
              placeholder="Enter Last Name"
              required
              disabled={!edit}
            />
          </Grid>
        </Grid>

        <Grid item xs={12} md={6}>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <FormPhoneInput
              name="phoneNumber"
              control={control}
              placeholder="Enter Mobile Number"
              title="Phone Number"
              required={true}
              disabled={!edit}
              rules={{
                required: "Phone number is required",
                pattern: {
                  value: /^91[0-9]{10}$/,
                  message: "Please enter a valid 10-digit phone number",
                },
              }}
            />

            {/* Send OTP button - only show in edit mode when phone number changes */}
            {edit && phoneChanged && (
              <Button
                variant="contained"
                color="primary"
                onClick={handleSendOtp}
                disabled={otpLoading}
                sx={{
                  alignSelf: "flex-end",
                  textWrap: "nowrap",
                  height: "40px",
                  fontSize: "12px",
                  bgcolor: "hsla(242, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(37, 34, 110)" },
                }}
              >
                {otpLoading ? "Sending..." : "Send OTP"}
              </Button>
            )}
          </Box>

          {/* OTP input field - only show when OTP has been sent */}
          {edit && phoneChanged && otpSent && (
            <Box sx={{ mt: 2 }}>
              <Typography>
                Enter Otp<span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="otp"
                placeholder="Enter OTP*"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                fullWidth
                size="small"
                variant="outlined"
                autoFocus
                inputProps={{
                  maxLength: 6,
                  inputMode: "numeric",
                  onKeyDown: (event) => {
                    const allowedKeys = [
                      "Backspace",
                      "Enter",
                      "Delete",
                      "ArrowLeft",
                      "ArrowRight",
                      "Tab",
                      "Home",
                      "End",
                    ];

                    if (
                      !/[0-9]/.test(event.key) &&
                      !allowedKeys.includes(event.key) &&
                      !(event.ctrlKey && event.key.toLowerCase() === "v") && // Allow Ctrl+V
                      !(event.metaKey && event.key.toLowerCase() === "v") // Allow Cmd+V on Mac
                    ) {
                      event.preventDefault();
                    }
                  },
                }}
                sx={{
                  bgcolor: "white",
                  borderRadius: 1,
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "#000",
                      borderWidth: "1.5px",
                    },
                    "&:hover fieldset": {
                      borderColor: "#000",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#000",
                    },
                  },
                }}
              />
            </Box>
          )}
        </Grid>

        <Grid item xs={12} md={6}>
          <Typography
            variant="h6"
            sx={{
              textAlign: "start",
              p: "0px !important",
              m: "0px !important",
            }}
          >
            Email ID
          </Typography>

          <TextField
            fullWidth
            variant="outlined"
            margin="normal"
            value={prefetch.email || getValues("email")}
            placeholder={"Enter email"}
            disabled
            sx={{ minHeight: 70 }}
            specialCharAllowed={true}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="fideId"
            control={control}
            title="FIDE ID"
            placeholder="Enter FIDE ID"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="aicfId"
            control={control}
            title="AICF ID"
            placeholder="Enter AICF ID"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormPhoneInput
            name="alternateContact"
            control={control}
            title="Alternate Contact"
            placeholder="Enter Alternate Contact"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="country"
            control={control}
            options={countries}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            getOptionLabel={(option) => option.name}
            title="Country"
            placeholder="Select Country"
            onChange={(_, newValue) => {
              if (newValue) {
                setValue("country", newValue.name);
                setValue("countryCode", newValue.isoCode);
                setValue("state", "");
                setValue("stateCode", "");
                setValue("city", "");
              }
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="state"
            control={control}
            options={states}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            getOptionLabel={(option) => option.name}
            title="State"
            placeholder="Select State"
            disabled={!countryCode}
            onChange={(_, newValue) => {
              if (newValue) {
                setValue("state", newValue.name);
                setValue("stateCode", newValue.isoCode);
                setValue("city", "");
              }
            }}
          />
        </Grid>

        {getValues("country") === "India" && (
          <Grid item xs={12} md={6}>
            <FormAutocomplete
              name="district"
              control={control}
              sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
              title="District"
              placeholder="Select District"
              options={districts}
              getOptionLabel={(option) => option.name}
              disabled={!stateCode || !countryCode}
              onChange={(_, newValue) => {
                if (newValue) {
                  setValue("district", newValue.name);
                }
              }}
            />
          </Grid>
        )}
        {(getValues("country") !== "India" || getValues("country") === "") && (
          <Grid item xs={12} md={6}>
            <FormTextField
              name="district"
              control={control}
              title="District"
              placeholder="Enter District"
            />
          </Grid>
        )}

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="city"
            control={control}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            options={cities}
            getOptionLabel={(option) => option.name}
            title="City"
            placeholder="Select City"
            disabled={!stateCode || !countryCode}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="pincode"
            control={control}
            title="Pincode"
            placeholder="Enter Pincode"
            maxLength={6}
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
            }}
          />
        </Grid>

        {/* Submit Buttons */}
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 2,
              mt: 6,
            }}
          >
            <Button
              type="submit"
              variant="contained"
              sx={{
                fontSize: 16,
                px: 2,
                bgcolor: isValid
                  ? "hsla(120, 49%, 35%, 1)"
                  : "hsla(0, 3%, 80%, 1)",
                "&:hover": {
                  bgcolor: "rgb(39, 104, 39)",
                },
              }}
              size="large"
             
            >
              {edit ? "Update" : "Create"}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </form>
  );
};

export default ArbiterProfileForm;

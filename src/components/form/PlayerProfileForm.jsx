import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON>po<PERSON>,
  <PERSON>rid,
  Box,
  Avatar,
  FormControlLabel,
  Checkbox,
  TextField,
} from "@mui/material";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, Controller } from "react-hook-form";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate } from "react-router-dom";
import FormTextField from "../../components/form/FormTextField";
import FormPhoneInput from "../../components/form/FormPhoneInput";
import FormAutocomplete from "../../components/form/FormAutocomplete";
import FormSelectField from "./FormSelectField";
import { Edit } from "@mui/icons-material";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import { playerProfileSchema, playerProfileEditSchema } from "../../schema/zod";
import Profile<PERSON>mageUpload from "../common/ProfileImageUpload";
import <PERSON><PERSON><PERSON><PERSON><PERSON>ield from "./FormNumberField";
import DocumentUpload from "../documents/DocumentUpload";
import UploadComponent from "../UploadComponent";
import ClubAutocomplete from "./ClubAutocomplete";

const PlayerProfileForm = ({ edit }) => {
  // Initialize React Hook Form

  const resolver = edit
    ? zodResolver(playerProfileEditSchema)
    : zodResolver(playerProfileSchema);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    getValues,
    reset,
  } = useForm({
    resolver,
    defaultValues: {
      playerTitle: "",
      firstName: "",
      lastName: "",
      otp: "",
      dob: "",
      gender: "male",
      alternateContact: "",
      fideRating: "",
      aicfId: "",
      districtId: "",
      phoneNumber: "",
      club: "",
      clubs: {},
      other_clubs: false,
      state: "",
      city: "",
      pincode: "",
      address: "",
      parentGuardianName: "",
      emergencyContact: "",
      fideId: "",
      stateId: "",
      association: "",
      country: "",
      district: "",
      termsAndConditions: false,
      countryCode: "",
      stateCode: "",
      profileUrl: null,
    },
    reValidateMode: "onChange",
  });

  // Watch for changes to country and state codes for dependent dropdowns
  const countryCode = watch("countryCode");
  const stateCode = watch("stateCode");

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [prefetch, setPrefetch] = useState({});
  const [originalProfileData, setOriginalProfileData] = useState({});
  const toast = UseToast();
  const { user } = UseGlobalContext();

  const navigate = useNavigate();

  // OTP logic
  const [originalPhone, setOriginalPhone] = useState(""); // Store original phone
  const [otpSent, setOtpSent] = useState(false);
  const [otpLoading, setOtpLoading] = useState(false);

  // Calculate if player is under 18 based on date of birth
  const dob = watch("dob");
  const isUnder18 = React.useMemo(() => {
    if (!dob) return false;

    const currentDate = new Date();
    const dobDate = new Date(dob);

    // Calculate age accurately
    let age = currentDate.getFullYear() - dobDate.getFullYear();
    const monthDiff = currentDate.getMonth() - dobDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }

    return age < 18;
  }, [dob]);

  // Watch phone number for changes
  const phoneNumber = watch("phoneNumber");

  // Check if phone number has changed from original
  const phoneChanged = React.useMemo(() => {
    return (
      edit && originalPhone && phoneNumber && phoneNumber !== originalPhone
    );
  }, [edit, originalPhone, phoneNumber]);

  // const phoneChanged = true;

  // Load saved draft from localStorage on initial render
  useEffect(() => {
    // Check for saved draft in localStorage
    const savedDraft = localStorage.getItem("playerProfileDraft");
    if (savedDraft && !edit) {
      try {
        const draftData = JSON.parse(savedDraft);
        // Set form values from draft
        Object.keys(draftData).forEach((key) => {
          setValue(key, draftData[key]);
        });
        toast.info("Draft loaded successfully");
      } catch (error) {
        console.error("Error loading draft:", error);
      }
    }
  }, [edit]);

  // Load countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await Client.get("/location/countries");
        if (!response.data.success) {
          console.error("Failed to fetch countries");
          return;
        }
        setCountries(response.data.data);
      } catch (error) {
        console.error("Error fetching countries:", error);
      }
    };

    fetchCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    // Get current country value
    const country = getValues("country");

    if (country) {
      const countryObj = countries.find((c) => c.name === country);
      if (countryObj) {
        const fetchStates = async () => {
          try {
            const response = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              {
                params: { country: countryObj.isoCode },
              }
            );
            if (!response.data.success) {
              toast.error("Failed to load states");
              return;
            }
            setStates(response.data.data);

            // Only reset dependent fields if not in edit mode or if country has changed
            if (!edit || !getValues("state")) {
              setValue("state", "");
              setValue("stateCode", "");
              setValue("district", "");
              setValue("city", "");
            }
          } catch (error) {
            console.error("Error fetching states:", error);
            toast.error("Failed to load states. Please try again.");
          }
        };
        fetchStates();
      }
    } else {
      setStates([]);
      // Always reset when country is cleared
      setValue("state", "");
      setValue("stateCode", "");
      setValue("district", "");
      setValue("city", "");
    }
  }, [countries, setValue, edit, getValues("country")]);

  useEffect(() => {
    if (getValues("country") === "India" && getValues("state")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      const stateObj = states.find((s) => s.name === getValues("state"));
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);

          // Only reset district if not in edit mode or if state has changed
          if (!edit || !getValues("district")) {
            setValue("district", "");
          }
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
      // Always reset when state is cleared
      setValue("district", "");
    }
  }, [getValues("state"), countries, setValue, edit]);

  // Load cities when state changes
  useEffect(() => {
    // Get current country and state values
    const country = getValues("country");
    const state = getValues("state");
    const countryCode = getValues("countryCode");
    const stateCode = getValues("stateCode");

    if (country && state && countryCode && stateCode) {
      const fetchCities = async () => {
        try {
          const response = await Client.get(
            `/location/cities/${countryCode}/${stateCode}`,
            {
              params: { country: countryCode, state: stateCode },
            }
          );
          if (!response.data.success) {
            toast.error("Failed to load cities");
            return;
          }
          setCities(response.data.data);

          // Only reset city if not in edit mode or if state has changed
          if (!edit || !getValues("city")) {
            setValue("city", "");
          }
        } catch (error) {
          console.error("Error fetching cities:", error);
          toast.error("Failed to load cities. Please try again.");
        }
      };
      fetchCities();
    } else {
      setCities([]);
      // Reset city only if state is cleared
      if (!state) {
        setValue("city", "");
      }
    }
  }, [getValues("state"), setValue, edit]);
  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data } = await Client.get("/user");
        const { email, phoneNumber, cbid, name } = data.data;
        setValue("phoneNumber", phoneNumber);
        setOriginalPhone(phoneNumber); // Set original phone
        const [firstName, lastName] = name && name.split(" ");
        setPrefetch({ email, phoneNumber, cbid, firstName, lastName });
        setOriginalProfileData({
          email,
          phoneNumber,
          cbid,
          firstName,
          lastName,
        });
        setValue("firstName", firstName);
        setValue("lastName", lastName);
      } catch (error) {
        console.error("Error fetching user data:", error);
        const errorMessage =
          error.response?.data?.message ||
          "An unexpected error occurred while fetching user data";
        toast.error(errorMessage);
      }
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!edit) return;

      try {
        const response = await Client.get("/player/profile");

        if (response.status === 204) {
          toast.info("You haven't created a profile yet.");
          return;
        }

        if (!response.data.success) {
          toast.info(response.data.message);
          return;
        }

        const playerDetails = response.data.data;

        // Process player details with proper club handling
        const processedDetails = {
          ...playerDetails,
          // Convert null values to appropriate defaults
          playerTitle: playerDetails.playerTitle || "",
          dob: playerDetails.dob || "",
          firstName: prefetch.firstName || "",
          lastName: prefetch.lastName || "",
          phoneNumber: prefetch.phoneNumber || "",
          gender: playerDetails.gender || "male",
          alternateContact: playerDetails.alternateContact || "",
          fideRating: playerDetails.fideRating || "",
          aicfId: playerDetails.aicfId || "",
          districtId: playerDetails.districtId || "",

          // Handle club data properly
          clubId: playerDetails.clubId || "",
          club: playerDetails.club || "",
          clubs: playerDetails.clubId
            ? {
                id: playerDetails.clubId,
                clubName: playerDetails.club,
                clubId: String(playerDetails.club).replace(/\s/g, "-"),
              }
            : {},
          other_clubs: playerDetails.clubId ? false : true,

          state: playerDetails.state || "",
          city: playerDetails.city || "",
          pincode: playerDetails.pincode || "",
          address: playerDetails.address || "",
          parentGuardianName: playerDetails.parentGuardianName || "",
          emergencyContact: playerDetails.emergencyContact || "",
          fideId: playerDetails.fideId || "",
          stateId: playerDetails.stateId || "",
          association: playerDetails.association || "",
          country: playerDetails.country || "",
          district: playerDetails.district || "",
          termsAndConditions: playerDetails.termsAndConditions || false,
        };

        // Reset form with processed details
        reset(processedDetails);

        // Store original data for comparison
        setOriginalProfileData((prev) => ({
          ...prev,
          ...processedDetails,
          clubId: playerDetails.clubId,
        }));

        // Your existing location handling code can go here...
      } catch (error) {
        console.error("Error fetching player profile:", error);
        let errorMessage = "Error fetching player data";

        if (error.response) {
          errorMessage = error.response.data?.message || "Server error";
        } else if (error.request) {
          errorMessage = "No response from server";
        } else {
          errorMessage = error.message;
        }

        toast.error("Failed to fetch player profile: " + errorMessage);
      }
    };

    fetchProfileData();
  }, [prefetch, edit]);

  const handleSendOtp = async () => {
    if (!edit) return; // Only in edit mode
    if (!phoneNumber) {
      toast.error("Please enter a valid phone number");
      return;
    }

    if (phoneNumber === originalPhone) {
      setOtpSent(false);
      setValue("otp", "");
      return;
    }

    // Validate phone number format before sending OTP
    if (!/^91[0-9]{10}$/.test(phoneNumber)) {
      toast.error("Phone number must start with 91 followed by 10 digits");
      return;
    }

    // Send OTP when phone number changes
    setOtpLoading(true);
    try {
      const response = await Client.post("/auth/send-otp", {
        phoneNumber,
        type: "verification",
        userId: user?.userId,
      });

      if (response.data.success) {
        setOtpSent(true);
        toast.success("OTP sent to new phone number");
      } else {
        setOtpSent(false);
        toast.error(response.data.message || "Failed to send OTP");
      }
    } catch (error) {
      console.error("Error sending OTP:", error);
      setOtpSent(false);
      if (error.response?.status === 429) {
        toast.error("wait 1 minute before sending another OTP");
        return;
      }
      toast.error(
        error.response?.data?.message || "Failed to send OTP. Try again."
      );
    } finally {
      setOtpLoading(false);
    }
  };

  // Helper to scroll to error field and show error toast
const onError = (errors) => {
  const firstField = Object.keys(errors)[0];
  const errorData = errors[firstField];

  const sectionElement = document.querySelector(`[name="${firstField}"]`);

  if (sectionElement) {
    const scrollOffset = 100;
    const y =
      sectionElement.getBoundingClientRect().top +
      window.pageYOffset -
      scrollOffset;
    window.scrollTo({ top: y, behavior: "smooth" });

    setTimeout(() => {
      if ("focus" in sectionElement) {
        sectionElement.focus();
      }
    }, 500);
  }

  toast.info(
    `Error in field: ${firstField} – ${errorData?.message || "Invalid input"}`
  );
};

  // Form submission handler
  const onSubmit = async (data) => {
    // Save form data to localStorage as backup (excluding file fields)
    const dataForStorage = { ...data };
    delete dataForStorage.profileImage;
    delete dataForStorage.profileImagePreview;
    localStorage.setItem("playerProfileDraft", JSON.stringify(dataForStorage));

    toast.info(`Processing your profile ${edit ? "update" : "creation"}...`);

    const value = {
      clubs: getValues("clubs"),
      club: getValues("club"),
      other_clubs: getValues("other_clubs"),
    };
    try {
      if (edit) {
        // Edit mode - only send changed fields
        const changedData = {};

        // Check each field for changes
        Object.keys(data).forEach((key) => {
          // Skip these fields from comparison
          if (
            [
              "otp",
              "phoneChanged",
              "profileImagePreview",
              "myfilePreview",
              "myfile",
            ].includes(key)
          )
            return;

          // Handle profile image
          if (key === "profileImage") {
            if (data.profileImage && data.profileImage instanceof File) {
              changedData.profileImage = data.profileImage;
            }
            return;
          }

          if (key === "clubs" || key === "club" || key === "other_clubs") {
            const hasClubChanges = checkClubChanges(value, originalProfileData);

            if (hasClubChanges.hasChanges) {
              if (value.other_clubs) {
                changedData.club = value.club || "";
                changedData.other_clubs = true;
              } else if (value.clubs && value.clubs.clubId) {
                changedData.clubs = value.clubs;
                changedData.other_clubs = false;
              } else {
                changedData.other_clubs = false;
                changedData.club = "";
              }
            }
            return;
          }

          // Compare other fields with original data
          if (
            JSON.stringify(data[key]) !==
            JSON.stringify(originalProfileData[key])
          ) {
            changedData[key] = data[key];
          }
        });

        // Always include user identification
        if (prefetch && prefetch.cbid) {
          changedData.cbid = prefetch.cbid;
        }

        // Handle phone number changes
        if (data.phoneNumber !== originalPhone) {
          changedData.phoneNumber = data.phoneNumber;
          changedData.phoneChanged = true;
          changedData.otp = data.otp || "";
        }

        // Handle name changes
        if (
          data.firstName !== originalProfileData.firstName ||
          data.lastName !== originalProfileData.lastName
        ) {
          changedData.name = `${data.firstName} ${data.lastName}`;
        }

        // Check if we have any changes to send
        if (
          Object.keys(changedData).length === 0 &&
          !changedData.profileImage
        ) {
          toast.info("No changes detected to update.");
          return;
        }

        // Send update request
        await sendUpdateRequest(changedData);
        return;
      }

      // Create mode - send data based on club selection
      await sendCreateRequest(data);
      sessionStorage.removeItem("new_user");
    } catch (error) {
      handleError(error);
    }
  };

  // Helper function to check club changes
  const checkClubChanges = (currentData, originalData) => {
    const currentOtherClubs = currentData.other_clubs || false;
    const originalOtherClubs = originalData.other_clubs || false;

    // Check if other_clubs flag changed
    const flagChanged = currentOtherClubs !== originalOtherClubs;

    let contentChanged = false;

    if (currentOtherClubs) {
      // Custom club mode - compare club strings
      const currentClub = currentData.club || "";
      const originalClub = originalData.club || "";
      contentChanged = currentClub !== originalClub;
    } else {
      // Regular club mode - compare clubs objects
      const currentClubs = currentData.clubs || {};
      const originalClubs = originalData.clubs || {};
      contentChanged =
        JSON.stringify(currentClubs) !== JSON.stringify(originalClubs);
    }

    return {
      hasChanges: flagChanged || contentChanged,
      flagChanged,
      contentChanged,
    };
  };

  // Helper function to send update request
  const sendUpdateRequest = async (changedData) => {
    const formData = new FormData();

    Object.keys(changedData).forEach((key) => {
      const value = changedData[key];

      if (key === "profileImage" && value instanceof File) {
        formData.append("profileImage", value);
      } else if (
        key === "clubs" &&
        typeof value === "object" &&
        value !== null
      ) {
        // Handle clubs object for regular clubs
        formData.append("clubs", JSON.stringify(value));
      } else if (key === "club") {
        // Handle club field for custom clubs (string)
        formData.append("club", String(value));
      } else if (typeof value === "object" && value !== null) {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, String(value));
      }
    });

    const response = await Client.put("/player/profile", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    handleResponse(response);
  };

  // Helper function to send create request
  const sendCreateRequest = async (data) => {
    const formData = new FormData();
    const value = {
      clubs: getValues("clubs"),
      club: getValues("club"),
      other_clubs: getValues("other_clubs"),
    };

    Object.keys(data).forEach((key) => {
      if (key === "profileImagePreview" || key === "myfilePreview") return;

      if (key === "myfile" && data[key] instanceof File) {
        formData.append("myfile", data[key]);
        return;
      }

      if (key === "profileImage" && data[key] instanceof File) {
        formData.append("profileImage", data[key]);
        return;
      }

      // Skip club-related fields here as we'll handle them separately below
      if (key === "clubs" || key === "club" || key === "other_clubs") {
        return;
      }

      // Handle all other fields
      if (data[key] !== undefined && data[key] !== null) {
        formData.append(key, String(data[key]));
      }
    });

    // Handle club-related fields separately and always append them
    // Always send other_clubs flag
    formData.append("other_clubs", String(Boolean(value.other_clubs)));

    if (value.other_clubs) {
      // Custom club mode - send club name and empty clubs object
      formData.append("club", String(value.club || ""));
      formData.append("clubs", JSON.stringify({})); // or omit this line if backend doesn't expect it
    } else {
      // Regular club mode - send clubs object and empty club string
      formData.append("club", ""); // empty club string
      if (
        value.clubs &&
        typeof value.clubs === "object" &&
        value.clubs !== null
      ) {
        formData.append("clubs", JSON.stringify(value.clubs));
      } else {
        formData.append("clubs", JSON.stringify({}));
      }
    }

    const response = await Client.post("/player/profile", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    handleResponse(response);
  };

  // Helper function to handle the API response
  const handleResponse = (response) => {
    if (!response.data.success) {
      if (response.data.zodErrors) {
        // Show the first Zod validation error
        const zodErrors = response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else {
        toast.error(response.data.error || "Unexpected error");
      }
      return;
    }

    toast.success(`Profile ${edit ? "updated" : "created"} successfully`);

    // Clear draft from localStorage after successful submission
    localStorage.removeItem("playerProfileDraft");

    // Reset the form
    reset();

    const redirectPath = localStorage.getItem("navigateTo");
    if (redirectPath) {
      localStorage.removeItem("navigateTo");
      navigate(redirectPath);
    } else {
      navigate("/dashboard/profile");
    }
  };

  // Helper function to handle errors
  const handleError = (error) => {
    console.error(`Error ${edit ? "updating" : "creating"} profile:`, error);

    if (error.response?.data?.error?.message === "Incorrect OTP.") {
      toast.error("Incorrect OTP. Please try again.");
    } else if (error.response?.status === 422 && error.response?.data?.data) {
      const validationErrors = error.response.data.data;
      Object.keys(validationErrors).forEach((key) => {
        if (key !== "_errors" && validationErrors[key]._errors) {
          toast.error(
            `Validation failed - ${key}: ${validationErrors[key]._errors.join(
              ", "
            )}`
          );
        }
      });
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error.message || "An error occurred");
    } else if (error.response?.data?.zodErrors) {
      // Handle Zod validation errors from the server
      const zodErrors = error.response.data.zodErrors;
      const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
      toast.error(firstZodError || "Please correct the validation errors");
    } else {
      const errorMessage =
        error.response?.data?.message ||
        `An unexpected error occurred while ${
          edit ? "updating" : "creating"
        } the profile.`;
      toast.error(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit,onError)}>
      <Grid
        container
        spacing={3}
        sx={{
          ".MuiFormControl-root": { mt: "4px !important" },
          ".MuiInputBase-input": { py: 1.5 },
          ".MuiAutocomplete-input": { p: "4px !important" },
          ".MuiGrid-root.MuiGrid-item": { pt: "0px !important" },
          position: "relative",
        }}
      >
        {/* CBID and Profile Image Row */}
        <Grid container item xs={12} md={12} sx={{ mb: 2 }}>
          <Grid item xs={6} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              CBID: {prefetch.cbid || ""}
            </Typography>
            <Grid item xs={12} md={6}>
              <FormSelectField
                name="playerTitle"
                control={control}
                options={[
                  { value: "Untitled", label: "Untitled" },
                  { value: "GM", label: "Grandmaster" },
                  { value: "IM", label: "International Master" },
                  { value: "FM", label: "FIDE Master" },
                  { value: "CM", label: "Candidate Master" },
                  { value: "WGM", label: "Woman Grandmaster" },
                  { value: "WIM", label: "Woman International Master" },
                  { value: "WFM", label: "Woman FIDE Master" },
                  { value: "WCM", label: "Woman Candidate Master" },
                ]}
                title="Player Title"
                placeholder="Select Player Title"
                rules={{ required: "Player Title is required" }}
              />
            </Grid>
          </Grid>
          <Grid item xs={6} display="flex" justifyContent="center" mb={2}>
            <ProfileImageUpload
              control={control}
              setValue={setValue}
              watch={watch}
            />
          </Grid>
        </Grid>

        {/* Basic Details Fields */}
        <Grid item xs={12} md={6} sx={{ display: "flex", gap: 2 }}>
          <Grid item xs={6}>
            <FormTextField
              name="firstName"
              control={control}
              title="First Name"
              maxLength={50}
              placeholder="Enter First Name"
              required
              disabled={!edit}
            />
          </Grid>
          <Grid item xs={6}>
            <FormTextField
              name="lastName"
              control={control}
              maxLength={50}
              title="Last Name"
              placeholder="Enter Last Name"
              required
              disabled={!edit}
            />
          </Grid>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormSelectField
            name="gender"
            control={control}
            options={[
              { value: "male", label: "Male" },
              { value: "female", label: "Female" },
              { value: "other", label: "Other" },
            ]}
            title="Gender"
            placeholder="Select Gender"
            required
            rules={{ required: "Gender is required" }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="dob"
            control={control}
            title="Date of Birth"
            placeholder="YYYY-MM-DD"
            required
            type="date"
            inputProps={{
              min: "1900-01-01",
              max: new Date().toISOString().split("T")[0],
            }}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <FormPhoneInput
              name="phoneNumber"
              control={control}
              placeholder="Enter Mobile Number"
              title="Phone Number"
              required={true}
              disabled={!edit}
              rules={{
                required: "Phone number is required",
                pattern: {
                  value: /^91[0-9]{10}$/,
                  message: "Please enter a valid 10-digit phone number",
                },
              }}
            />

            {/* Send OTP button - only show in edit mode when phone number changes */}
            {edit && phoneChanged && (
              <Button
                variant="contained"
                color="primary"
                onClick={handleSendOtp}
                disabled={otpLoading}
                sx={{
                  alignSelf: "flex-end",
                  textWrap: "nowrap",
                  height: "40px",
                  fontSize: "12px",
                  bgcolor: "hsla(242, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(37, 34, 110)" },
                }}
              >
                {otpLoading ? "Sending..." : "Send OTP"}
              </Button>
            )}
          </Box>

          {/* OTP input field - only show when OTP has been sent */}
          {edit && phoneChanged && otpSent && (
            <Box sx={{ mt: 2 }}>
              <FormTextField
                name="otp"
                control={control}
                title="Enter OTP"
                placeholder="Enter the OTP sent to your new phone number"
                required={phoneChanged}
                type="text"
                inputProps={{
                  maxLength: 6,
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                rules={{
                  required: phoneChanged
                    ? "OTP is required to verify your new phone number"
                    : false,
                  minLength: {
                    value: 6,
                    message: "OTP must be 6 digits",
                  },
                  maxLength: {
                    value: 6,
                    message: "OTP must be 6 digits",
                  },
                  validate: (value) => {
                    if (phoneChanged && (!value || value.length !== 6)) {
                      return "Please enter a valid 6-digit OTP to verify your new phone number";
                    }
                    return true;
                  },
                }}
              />
            </Box>
          )}
        </Grid>

        <Grid item xs={12} md={6}>
          <Typography
            variant="h6"
            sx={{
              textAlign: "start",
              p: "0px !important",
              m: "0px !important",
            }}
          >
            Email ID
          </Typography>

          <TextField
            fullWidth
            variant="outlined"
            margin="normal"
            value={prefetch.email || ""}
            placeholder={"Enter email"}
            disabled
            sx={{ minHeight: 70 }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormNumberField
            name="fideId"
            control={control}
            title="FIDE ID"
            placeholder="Enter FIDE ID"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormNumberField
            name="fideRating"
            control={control}
            title="FIDE Rating"
            placeholder="Enter FIDE Rating"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="aicfId"
            control={control}
            maxLength={20}
            title="AICF ID"
            placeholder="Enter AICF ID"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="stateId"
            control={control}
            title="State ID"
            maxLength={20}
            placeholder="Enter State ID"
            specialCharAllowed={true}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <FormTextField
            name="districtId"
            control={control}
            title="District ID"
            maxLength={20}
            placeholder="Enter District ID"
            specialCharAllowed={true}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <ClubAutocomplete
            name="clubs"
            control={control}
            setValue={setValue}
            watch={watch}
            prefetch={originalProfileData}
            edit={edit}
            required={true}
            title="Select Club"
            placeholder="Search for Clubs or type custom club name"
            fetchUrl="/club/get"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <FormPhoneInput
            name="alternateContact"
            control={control}
            title="Alternate Contact"
            placeholder="Enter Alternate Contact"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="parentGuardianName"
            control={control}
            title="Parent/Guardian Name"
            maxLength={100}
            placeholder="Enter Parent/Guardian Name"
            required={isUnder18}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormPhoneInput
            name="emergencyContact"
            control={control}
            title="Parent/Guardian Contact"
            placeholder="Enter Parent/Guardian Contact"
            required={isUnder18}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="association"
            control={control}
            maxLength={100}
            title="Association"
            placeholder="Enter Association"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="country"
            control={control}
            options={countries}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            getOptionLabel={(option) => option.name}
            title="Country"
            placeholder="Select Country"
            maxLength={50}
            required
            onChange={(_, newValue) => {
              if (newValue) {
                setValue("country", newValue.name);
                setValue("countryCode", newValue.isoCode);
                setValue("state", "");
                setValue("stateCode", "");
                setValue("city", "");
              }
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="state"
            control={control}
            options={states}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            getOptionLabel={(option) => option.name}
            title="State"
            placeholder="Select State"
            required
            disabled={!countryCode}
            onChange={(_, newValue) => {
              if (newValue) {
                setValue("state", newValue.name);
                setValue("stateCode", newValue.isoCode);
                setValue("city", "");
              }
            }}
          />
        </Grid>

        {getValues("country") === "India" && (
          <Grid item xs={12} md={6}>
            <FormAutocomplete
              name="district"
              control={control}
              sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
              title="District"
              placeholder="Select District"
              required
              options={districts}
              getOptionLabel={(option) => option.name}
              disabled={!stateCode || !countryCode}
              onChange={(_, newValue) => {
                if (newValue) {
                  setValue("district", newValue.name);
                }
              }}
            />
          </Grid>
        )}
        {(getValues("country") !== "India" || getValues("country") === "") && (
          <Grid item xs={12} md={6}>
            <FormTextField
              name="district"
              control={control}
              title="District"
              placeholder="Enter District"
              required
            />
          </Grid>
        )}

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="city"
            control={control}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            options={cities}
            getOptionLabel={(option) => option.name}
            title="City"
            placeholder="Select City"
            required
            disabled={!stateCode || !countryCode}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormNumberField
            name="pincode"
            control={control}
            title="Pincode"
            placeholder="Enter Pincode"
            required
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
              maxLength: 6,
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="address"
            control={control}
            title="Address"
            placeholder="Enter Address"
            maxLength={150}
            required
            multiline
            rows={3}
            specialCharAllowed={true}
          />
        </Grid>
        {sessionStorage.getItem("new_user") === "true" && (
          <Grid
            item
            xs={12}
            md={6}
            sx={{
              display: "flex",
              gap: 2,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <UploadComponent
              setValue={setValue}
              title="Upload Documents"
              watch={watch}
              fileName={"myfile"}
              filePreview={"myfilePreview"}
              errors={errors}
              size={10}
            />
          </Grid>
        )}

        {/* Terms and Conditions */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ textAlign: "start" }}>
            Declaration Statement
          </Typography>
          <Typography
            style={{ fontSize: 14 }}
            variant="h6"
            sx={{ textAlign: "start", fontSize: 14 }}
          >
            I will be solely responsible for my involvement in any kind of
            unlawful activities
          </Typography>
          <Controller
            name="termsAndConditions"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={field.value}
                    sx={{
                      color: "hsla(0, 3%, 80%, 1)",
                      "&.Mui-checked": {
                        color: "black",
                      },
                      "&.MuiCheckbox-indeterminate": {
                        color: "hsla(0, 3%, 80%, 1)",
                      },
                    }}
                    onChange={(e) => field.onChange(e.target.checked)}
                  />
                }
                label="I agree to the terms and conditions"
              />
            )}
          />
          {errors && errors.termsAndConditions && (
            <Typography
              variant="caption"
              color="error"
              sx={{ ml: 1.5, mt: 0.5 }}
            >
              {"Please agree to the terms and conditions"}
            </Typography>
          )}
        </Grid>

        {/* Submit Buttons */}
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 2,
              mt: 6,
            }}
          >
            <Button
              type="submit"
              variant="contained"
              sx={{
                fontSize: 16,
                px: 2,
                bgcolor: isValid
                  ? "hsla(120, 49%, 35%, 1)"
                  : "hsla(0, 3%, 80%, 1)",
                "&:hover": {
                  bgcolor: isValid ? "rgb(39, 104, 39)" : "hsla(0, 3%, 80%, 1)",
                },
              }}
              size="large"
              
            >
              {edit ? "Update" : "Create"}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </form>
  );
};

export default PlayerProfileForm;

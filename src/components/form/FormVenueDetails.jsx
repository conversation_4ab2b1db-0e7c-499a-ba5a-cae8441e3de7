import React, { useEffect, useState } from "react";
import { Grid, Typography, Box } from "@mui/material";
import AutoNavigateMapComponent from "../AutoNavigateMapComponent";
import UploadComponent from "../UploadComponent";
import <PERSON><PERSON><PERSON><PERSON><PERSON>ield from "./FormTextField";
import Form<PERSON><PERSON>ber<PERSON>ield from "./FormNumberField";
import FormAutocomplete from "./FormAutocomplete";
import { Client } from "../../api/client";

/**
 * FormVenueDetails - A form component for venue details using React Hook Form
 *
 * @param {Object} props - Component props
 * @param {Object} props.control - React Hook Form control object
 * @param {Function} props.setValue - React Hook Form setValue function
 * @param {Object} props.watch - React Hook Form watch function
 */
const FormVenueDetails = ({ control, setValue, watch, getValues, errors }) => {
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);

  // Watch for country and state changes
  const countryCode = watch("countryCode");
  const stateCode = watch("stateCode");

  // Load countries on component mount and handle initial data for edit mode
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await Client.get("/location/countries");
        if (!response.data.success) return;
        setCountries(response.data.data);

        // If we already have country and state values (editing mode), load the states and cities
        const currentCountry = getValues("country");
        const currentState = getValues("state");

        if (currentCountry && response.data.data.length > 0) {
          const countryObj = response.data.data.find(
            (c) => c.name === currentCountry
          );
          if (countryObj) {
            // Set the country code if not already set
            if (!getValues("countryCode")) {
              setValue("countryCode", countryObj.isoCode);
            }

            // Load states for this country
            const statesResponse = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              { params: { country: countryObj.isoCode } }
            );

            if (statesResponse.data.success) {
              setStates(statesResponse.data.data);

              // If we have a state value, load cities
              if (currentState) {
                const stateObj = statesResponse.data.data.find(
                  (s) => s.name === currentState
                );
                if (stateObj) {
                  // Set the state code if not already set
                  if (!getValues("stateCode")) {
                    setValue("stateCode", stateObj.isoCode);
                  }

                  // Load cities for this state
                  const citiesResponse = await Client.get(
                    `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
                    {
                      params: {
                        country: countryObj.isoCode,
                        state: stateObj.isoCode,
                      },
                    }
                  );

                  if (citiesResponse.data.success) {
                    setCities(citiesResponse.data.data);
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching location data:", error);
      }
    };

    fetchCountries();
  }, [getValues, setValue]);

  // Watch for country and state values
  const country = watch("country");
  const state = watch("state");

  // Load states when country changes
  useEffect(() => {
    if (country && countries.length > 0) {
      const countryObj = countries.find((c) => c.name === country);
      if (countryObj) {
        const fetchStates = async () => {
          try {
            const response = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              {
                params: { country: countryObj.isoCode },
              }
            );
            if (!response.data.success) return;
            setStates(response.data.data);

            // Only reset dependent fields if state is empty
            if (!getValues("state")) {
              setValue("state", "");
              setValue("stateCode", "");
              setValue("city", "");
              setValue("district", "");
            }
          } catch (error) {
            console.error("Error fetching states:", error);
          }
        };
        fetchStates();
      }
    } else if (!country) {
      setStates([]);
      setValue("state", "");
      setValue("stateCode", "");
      setValue("district", "");
      setValue("city", "");
    }
  }, [country, countries, setValue, getValues]);

  // We already have country and state codes from above
  // Load districts when country is India and state changes
  useEffect(() => {
    const shouldFetchDistricts =
      country === "India" && countryCode && stateCode;

    if (shouldFetchDistricts) {
      const fetchDistricts = async () => {
        try {
          const response = await Client.get(
            `/location/districts/${stateCode}`,
            {
              params: { country: countryCode, state: stateCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);

          // Only reset if current value doesn't match any district
          const currentDistrict = getValues("district");
          const found = response.data.data.find(
            (d) => d.name === currentDistrict
          );
          if (!found) {
            setValue("district", "");
          }
        } catch (error) {
          console.error("Error fetching districts:", error);
        }
      };

      fetchDistricts();
    } else {
      setDistricts([]);

      // Only reset if the current country was previously India
      if (districts.length > 0) {
        setValue("district", "");
      }
    }
  }, [country, countryCode, stateCode, getValues, setValue]);

  // Load cities when state or country changes
  useEffect(() => {
    if (country && state && countries.length > 0 && states.length > 0) {
      const countryObj = countries.find((c) => c.name === country);
      const stateObj = states.find((s) => s.name === state);

      if (countryObj && stateObj) {
        const fetchCities = async () => {
          try {
            const response = await Client.get(
              `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
              {
                params: {
                  country: countryObj.isoCode,
                  state: stateObj.isoCode,
                },
              }
            );
            if (!response.data.success) return;
            setCities(response.data.data);

            // Only reset city if it's empty
            if (!getValues("city")) {
              setValue("city", "");
            }
          } catch (error) {
            console.error("Error fetching cities:", error);
          }
        };
        fetchCities();
      }
    } else {
      setCities([]);
      // Reset city when state is cleared
      if (!state) {
        setValue("city", "");
      }
      // Reset district and city when country is cleared
      if (!country) {
        setValue("district", "");
        setValue("city", "");
      }
    }
  }, [country, state, setValue]);

  // Handle country selection
  const handleCountryChange = (selectedCountry) => {
    if (selectedCountry) {
      setValue("country", selectedCountry.name);
      setValue("countryCode", selectedCountry.isoCode);
      setValue("state", "");
      setValue("stateCode", "");
      setValue("city", "");
    } else {
      setValue("country", "");
      setValue("countryCode", "");
    }
  };
  const handleDistrictChange = (selectedDistrict) => {
    if (selectedDistrict) {
      setValue("district", selectedDistrict.name);
    } else {
      setValue("district", "");
    }
  };

  // Handle state selection
  const handleStateChange = (selectedState) => {
    if (selectedState) {
      setValue("state", selectedState.name);
      setValue("stateCode", selectedState.isoCode);
      setValue("city", "");
    } else {
      setValue("state", "");
      setValue("stateCode", "");
    }
  };

  // Handle city selection
  const handleCityChange = (selectedCity) => {
    if (selectedCity) {
      setValue("city", selectedCity.name);
    } else {
      setValue("city", "");
    }
  };

  return (
    <>
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mt: 6, mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        Tournament Venue Details:
      </Typography>
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="country"
            control={control}
            title="Country"
            placeholder="Select Country"
            required
            options={countries}
            getOptionLabel={(option) => option.name}
            onChange={(_, newValue) => handleCountryChange(newValue)}
          />

          <FormAutocomplete
            name="state"
            control={control}
            title="State"
            placeholder="Select State"
            required
            options={states}
            getOptionLabel={(option) => option.name}
            disabled={!countryCode}
            onChange={(_, newValue) => handleStateChange(newValue)}
          />
          {country === "India" && (
            <FormAutocomplete
              name="district"
              control={control}
              title="District"
              placeholder="Select District"
              required
              options={districts}
              getOptionLabel={(option) => option.name}
              disabled={!stateCode || !countryCode}
              onChange={(_, newValue) => handleDistrictChange(newValue)}
            />
          )}
          {(country !== "India" || country === "") && (
            <FormTextField
              name="district"
              control={control}
              maxLength={50}
              title="District"
              placeholder="Enter District"
              required
            />
          )}

          <FormAutocomplete
            name="city"
            control={control}
            title="City"
            placeholder="Select City"
            required
            options={cities}
            getOptionLabel={(option) => option.name}
            disabled={!stateCode || !countryCode}
            onChange={(_, newValue) => handleCityChange(newValue)}
          />

          <FormNumberField
            name="pincode"
            control={control}
            title="Pincode"
            placeholder="Enter Pincode"
            maxLength={6}
          />

          <FormTextField
            name="nearestLandmark"
            control={control}
            title="Nearest Landmark"
            maxLength={100}
            placeholder="Enter Nearest Landmark"
            specialCharAllowed={true}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <FormTextField
            name="venueAddress"
            maxLength={250}
            control={control}
            title="Venue Address"
            placeholder="Enter Complete Venue Address"
            required
            multiline
            rows={4}
            specialCharAllowed={true}
          />

          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" sx={{ textAlign: "start", mb: 2 }}>
              Add Location
            </Typography>
            <AutoNavigateMapComponent
              control={control}
              name="locationUrl"
              countryField="country"
              stateField="state"
              cityField="city"
            />
          </Box>
          <UploadComponent
            control={control}
            watch={watch}
            setValue={setValue}
            fileName={"brochure"}
            filePreview={"brochureFilePreview"}
            errors={errors}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default FormVenueDetails;

import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Card,
  CardContent,
  Autocomplete,
  CircularProgress,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  Badge,
  Tab,
  Tabs,
  LinearProgress,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link, useLocation } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import FilterListIcon from "@mui/icons-material/Filter";
import GetAppIcon from "@mui/icons-material/GetApp";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PendingIcon from "@mui/icons-material/Pending";
import ErrorIcon from "@mui/icons-material/Error";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const ClubPayoutSummaryPage = () => {
  const { state } = useLocation();
  const clubId = state?.clubId; // Get tournament title from URL if coming from PaymentDetails
  const [payouts, setPayouts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [page, setPage] = useState(1);

  const limit = 10;

  // Summary statistics
  const [payoutStats, setPayoutStats] = useState({
    totalPayouts: 0,
    totalAmount: 0,
    pendingPayouts: 0,
    completedPayouts: 0,
    failedPayouts: 0,
    totalPendingAmount: 0,
  });

  // Filters
  const [filters, setFilters] = useState({
    status: "all",
    tournament: "",
    dateFrom: "",
    dateTo: "",
    urgent: "all",
  });

  // Dialog states
  const [selectedPayout, setSelectedPayout] = useState(null);
  const [showPayoutDetails, setShowPayoutDetails] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const toast = UseToast();

  // Fetch payouts with filters
  const fetchPayouts = useCallback(
    async (pageNumber = 1, currentFilters = filters) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
          ...(currentFilters.status !== "all" && {
            status: currentFilters.status,
          }),
          ...(currentFilters.tournament && {
            tournament: currentFilters.tournament,
          }),
          ...(currentFilters.dateFrom && {
            date_from: currentFilters.dateFrom,
          }),
          ...(currentFilters.dateTo && { date_to: currentFilters.dateTo }),
          ...(currentFilters.urgent !== "all" && {
            urgent: currentFilters.urgent === "yes",
          }),
        };

        const response = await Client.get(`/admin-payout/list/${clubId}`, {
          params,
        });

        if (response.data.success) {
          setPayouts(response.data.data.payouts || []);
          setTotalPages(response.data.data.pagination?.total_pages || 0);

          // Calculate statistics
          calculatePayoutStats(response.data.data.payouts || []);
        } else {
          setPayouts([]);
          setTotalPages(0);
          toast.info("No payouts found");
        }
      } catch (error) {
        console.error("Error fetching payouts:", error.response?.data?.error || error.message);
        if (error instanceof AxiosError && error.response) {
          toast.error(error.response.data.error || "Failed to fetch payouts");
        } else {
          toast.error("An error occurred while fetching payouts");
        }
      } finally {
        setLoading(false);
      }
    },
    [filters, limit]
  );

  // Calculate payout statistics
  const calculatePayoutStats = (payoutsList) => {
    const stats = {
      totalPayouts: payoutsList.length,
      totalAmount: 0,
      pendingPayouts: 0,
      completedPayouts: 0,
      failedPayouts: 0,
      totalPendingAmount: 0,
    };

    payoutsList.forEach((payout) => {
      stats.totalAmount += parseFloat(payout.amount || 0);

      const status = payout.status?.toLowerCase();
      if (status === "processed") {
        stats.completedPayouts++;
      } else if (status === "pending" || status === "queued") {
        stats.pendingPayouts++;
        stats.totalPendingAmount += parseFloat(payout.amount || 0);
      } else if (status === "failed" || status === "cancelled") {
        stats.failedPayouts++;
      }
    });

    setPayoutStats(stats);
  };



  // Handle page change
  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayouts(value);
  };

  // Handle tab change

  // Apply filters
  const applyFilters = () => {
    setPage(1);
    fetchPayouts(1);
    setShowFilters(false);
  };

  // Reset filters
  const resetFilters = () => {
    const resetFilters = {
      status: "all",
      tournament: "",
      dateFrom: "",
      dateTo: "",
      urgent: "all",
    };
    setFilters(resetFilters);
    setPage(1);
    fetchPayouts(1, resetFilters);
    setShowFilters(false);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "processed":
        return "success";
      case "pending":
      case "queued":
        return "warning";
      case "failed":
      case "cancelled":
        return "error";
      default:
        return "default";
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case "processed":
        return <CheckCircleIcon fontSize="small" />;
      case "pending":
      case "queued":
        return <PendingIcon fontSize="small" />;
      case "failed":
      case "cancelled":
        return <ErrorIcon fontSize="small" />;
      default:
        return <PendingIcon fontSize="small" />;
    }
  };

  // View payout details
  const viewPayoutDetails = (payout) => {
    setSelectedPayout(payout);
    setShowPayoutDetails(true);
  };

  useEffect(() => {
    if (clubId) {
      fetchPayouts(1);
    }
  }, [clubId]);



  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, pb: 8, minHeight: "100vh" }}>
      <BackButton />

      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography variant="h4" fontWeight="bold" color="primary">
          <AccountBalanceIcon sx={{ mr: 1, verticalAlign: "middle" }} />
          Payout Tracking
        </Typography>

        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(true)}
          >
            Filters
          </Button>
        </Box>
      </Box>

      {/* Summary Statistics */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontWeight: "bold", color: "#3f51b5" }}
        >
          Payout Summary
        </Typography>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Total Payouts
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {payoutStats.totalPayouts}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <TrendingUpIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  All Time
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  ₹{payoutStats.totalAmount.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <MonetizationOnIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Processed
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Loading indicator */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Payouts Table */}
      <DynamicTable
        columns={[
          {
            id: "payout_id",
            label: "Payout ID",
            format: (_, payout) => (
              <Typography
                variant="body2"
                fontFamily="monospace"
                sx={{ cursor: "pointer", color: "primary.main" }}
                onClick={() => viewPayoutDetails(payout)}
              >
                {payout.payout_id?.substring(0, 12)}...
              </Typography>
            ),
          },
          {
            id: "tournament",
            label: "Tournament",
            format: (_, payout) =>
              payout.tournament ? (
                <Link
                  to={`/tournaments/${payout.tournament.title}`}
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <Typography variant="body2" fontWeight="medium">
                    {payout.tournament.title?.replace(/-/g, " ") || "N/A"}
                  </Typography>
                </Link>
              ) : (
                "N/A"
              ),
          },
          {
            id: "amount",
            label: "Amount",
            format: (_, payout) => (
              <Typography variant="h6" fontSize="16px" fontWeight="bold">
                ₹{parseFloat(payout.amount || 0).toLocaleString()}
              </Typography>
            ),
          },
          {
            id: "status",
            label: "Status",
            format: (_, payout) => (
              <Chip
                icon={getStatusIcon(payout.status)}
                label={payout.status?.toUpperCase() || "UNKNOWN"}
                color={getStatusColor(payout.status)}
                size="small"
              />
            ),
          },
          {
            id: "mode",
            label: "Mode",
            format: (_, payout) => payout.mode?.toUpperCase() || "N/A",
          },
          {
            id: "urgent",
            label: "Priority",
            format: (_, payout) =>
              payout.urgent_transfer ? (
                <Chip label="URGENT" color="error" size="small" />
              ) : (
                <Chip label="NORMAL" color="default" size="small" />
              ),
          },
          {
            id: "created_at",
            label: "Created",
            format: (_, payout) => formatDate(payout.created_at),
          },
          {
            id: "actions",
            label: "Actions",
            format: (_, payout) => (
              <Box display="flex" gap={0.5}>
                <Tooltip title="View Details">
                  <IconButton
                    size="small"
                    onClick={() => viewPayoutDetails(payout)}
                  >
                    <VisibilityIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            ),
          },
        ]}
        data={payouts}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        showDetailsButton={false}
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />

      {/* Filters Dialog */}
      <Dialog
        open={showFilters}
        onClose={() => setShowFilters(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Filter Payouts</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                select
                fullWidth
                label="Status"
                value={filters.status}
                onChange={(e) =>
                  setFilters({ ...filters, status: e.target.value })
                }
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="queued">Queued</MenuItem>
                <MenuItem value="processed">Processed</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                select
                fullWidth
                label="Priority"
                value={filters.urgent}
                onChange={(e) =>
                  setFilters({ ...filters, urgent: e.target.value })
                }
              >
                <MenuItem value="all">All Priorities</MenuItem>
                <MenuItem value="yes">Urgent</MenuItem>
                <MenuItem value="no">Normal</MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="From Date"
                type="date"
                value={filters.dateFrom}
                onChange={(e) =>
                  setFilters({ ...filters, dateFrom: e.target.value })
                }
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="To Date"
                type="date"
                value={filters.dateTo}
                onChange={(e) =>
                  setFilters({ ...filters, dateTo: e.target.value })
                }
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Tournament Name"
                value={filters.tournament}
                onChange={(e) =>
                  setFilters({ ...filters, tournament: e.target.value })
                }
                placeholder="Enter tournament name"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowFilters(false)}>Cancel</Button>
          <Button onClick={resetFilters} color="warning">
            Reset
          </Button>
          <Button variant="contained" onClick={applyFilters}>
            Apply Filters
          </Button>
        </DialogActions>
      </Dialog>

      {/* Payout Details Dialog */}
      <Dialog
        open={showPayoutDetails}
        onClose={() => setShowPayoutDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="h6" fontWeight="bold">
              Payout Details
            </Typography>
            <Chip
              icon={getStatusIcon(selectedPayout?.status)}
              label={selectedPayout?.status?.toUpperCase() || "UNKNOWN"}
              color={getStatusColor(selectedPayout?.status)}
            />
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedPayout && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Card sx={{ bgcolor: "#f5f9ff" }}>
                  <CardContent>
                    <Typography
                      variant="subtitle1"
                      fontWeight="medium"
                      gutterBottom
                    >
                      Basic Information
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Payout ID:</strong> {selectedPayout.payout_id}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Amount:</strong> ₹
                      {parseFloat(selectedPayout.amount || 0).toLocaleString()}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Mode:</strong>{" "}
                      {selectedPayout.mode?.toUpperCase() || "N/A"}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Reference ID:</strong>{" "}
                      {selectedPayout.reference_id || "N/A"}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>UTR:</strong> {selectedPayout.utr || "N/A"}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card sx={{ bgcolor: "#f1f8e9" }}>
                  <CardContent>
                    <Typography
                      variant="subtitle1"
                      fontWeight="medium"
                      gutterBottom
                    >
                      Timeline
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Created:</strong>{" "}
                      {formatDate(selectedPayout.created_at)}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Processed:</strong>{" "}
                      {formatDate(selectedPayout.processed_at)}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Priority:</strong>{" "}
                      {selectedPayout.urgent_transfer ? "Urgent" : "Normal"}
                    </Typography>
                    {selectedPayout.failure_reason && (
                      <Typography variant="body2" color="error" gutterBottom>
                        <strong>Failure Reason:</strong>{" "}
                        {selectedPayout.failure_reason}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {selectedPayout.tournament && (
                <Grid item xs={12}>
                  <Card sx={{ bgcolor: "#fff3e0" }}>
                    <CardContent>
                      <Typography
                        variant="subtitle1"
                        fontWeight="medium"
                        gutterBottom
                      >
                        Tournament Information
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Name:</strong>{" "}
                        {selectedPayout.tournament.name?.replace(/-/g, " ")}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Participants:</strong>{" "}
                        {selectedPayout.tournament.participants || "N/A"}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Status:</strong>{" "}
                        {selectedPayout.tournament.status || "N/A"}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}

              {(selectedPayout.fees || selectedPayout.tax) && (
                <Grid item xs={12}>
                  <Card sx={{ bgcolor: "#fce4ec" }}>
                    <CardContent>
                      <Typography
                        variant="subtitle1"
                        fontWeight="medium"
                        gutterBottom
                      >
                        Fee Breakdown
                      </Typography>
                      {selectedPayout.fees && (
                        <Typography variant="body2" gutterBottom>
                          <strong>Processing Fees:</strong> ₹
                          {selectedPayout.fees}
                        </Typography>
                      )}
                      {selectedPayout.tax && (
                        <Typography variant="body2" gutterBottom>
                          <strong>Tax:</strong> ₹{selectedPayout.tax}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            variant="contained"
            onClick={() => setShowPayoutDetails(false)}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ClubPayoutSummaryPage;

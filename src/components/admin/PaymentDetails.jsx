import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link, useParams, useNavigate } from "react-router-dom";
import PaymentIcon from "@mui/icons-material/Payment";
import PersonIcon from "@mui/icons-material/Person";
import EventIcon from "@mui/icons-material/Event";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const PaymentTournamentDetails = () => {
  const [clubPayments, setClubPayments] = useState([]);
  const [playerPayments, setPlayerPayments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedTournament, setSelectedTournament] = useState(null);
  const [summaryStats, setSummaryStats] = useState({
    totalPayers: 0,
    totalAmount: 0,
    currency: "INR",
    successfulPayments: 0,
    pendingPayments: 0,
    failedPayments: 0,
  });
  // States for form inputs
  const [search, setSearch] = useState({
    transactionId: "",
    tournamentTitle: "",
    paymentType: "all",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();
  const { title: id } = useParams();
  const navigate = useNavigate();

  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayments(value);
  };

  // Define fetchPayments before using it in useEffect
  const fetchPayments = useCallback(
    async (pageNumber) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
          tournamentTitle: id,
        };

        const response = await Client.get("/admin/details/tournament/payment", {
          params,
        });

        if (response.status === 204) {
          toast.info("No payment records found");
          setClubPayments([]);
          setPlayerPayments([]);
          setTotalPages(0);
          setPage(1);
          setSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
          });
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        const paymentsData = response.data.data.payments;

        // Split payments by type
        const clubPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "club"
        );
        const playerPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "player"
        );

        setClubPayments(clubPaymentsData);
        setPlayerPayments(playerPaymentsData);

        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);

        // Calculate and set summary statistics
        const stats = {
          totalPayers: 0,
          totalAmount: 0,
          currency: "INR",
          successfulPayments: 0,
          pendingPayments: 0,
          failedPayments: 0,
        };
        stats.totalPayers = paymentsData.reduce((acc, payment) => {
          if (
            search.paymentType === "all" ||
            payment.paymentType === search.paymentType
          ) {
            if (payment.paymentType === "club") {
              acc += payment.playersCount || 0;
            } else if (payment.paymentType === "player") {
              acc += 1;
            }
          }
          return acc;
        }, 0);

        paymentsData.forEach((payment) => {
          // Add to total amount
          stats.totalAmount += parseFloat(payment.paymentAmount || 0);

          // Count by status
          const status = payment.paymentStatus?.toLowerCase();
          if (status === "paid" || status === "success") {
            stats.successfulPayments++;
          } else if (status === "pending") {
            stats.pendingPayments++;
          } else if (status === "failed") {
            stats.failedPayments++;
          }

          // Set currency if available
          if (payment.paymentCurrency) {
            stats.currency = payment.paymentCurrency;
          }
        });

        setSummaryStats(stats);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [id, limit, search.paymentType]
  );

  useEffect(() => {
    fetchPayments(1);
  }, [fetchPayments]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  const fetchPaymentReport = async () => {
    // return;
    // setLoading(true);

    try {
      const response = await Client.get("/report/payment", {
        params: { ...search },
        responseType: "blob",
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Players_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };



  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 4,
        pt: 2,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <BackButton />
      {(clubPayments?.length > 0 || playerPayments?.length > 0) && (
        <Box
          mb={2}
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          <Button
            size="small"
            disabled={!clubPayments?.length && !playerPayments?.length}
            variant="contained"
            onClick={fetchPaymentReport}
          >
            Download report
          </Button>
        </Box>
      )}

      {/* Summary Statistics */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontWeight: "bold", color: "#3f51b5" }}
        >
          Payment Summary
        </Typography>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Total Payers
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.totalPayers}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <PersonIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Players
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.totalAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Collected
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Refund Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.currency} {summaryStats.refoundAmount || 0}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Refunded
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Payout Management Section */}
      {(clubPayments?.length > 0 || playerPayments?.length > 0) && (
        <Paper
          sx={{
            mb: 3,
            p: 3,
            bgcolor: "#fff8e1",
            borderRadius: 2,
            boxShadow: 2,
          }}
        >
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Box>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ fontWeight: "bold", color: "#f57c00", mb: 1 }}
              >
                <AccountBalanceIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                Tournament Payout Management
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Manage payouts for this tournament. View, create, and track
                payout status.
              </Typography>
            </Box>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AccountBalanceIcon />}
              onClick={() =>
                navigate(`/dashboard/payments/${encodeURIComponent(id)}/payout`)
              }
              sx={{ minWidth: 150 }}
            >
              Manage Payouts
            </Button>
          </Box>
        </Paper>
      )}

      {/* Club Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "club") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Club Payments
          </Typography>
          <DynamicTable
            columns={[
              {
                id: "clubName",
                label: "ClubName",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/clubs/${payment.clubId}`}
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.clubName || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "Transaction ID",
                format: (_, payment) => payment.paymentTransactionId || "N/A",
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "playersCount",
                label: "Players Count",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    fontSize={"16px"}
                    fontWeight="medium"
                  >
                    {payment.playersCount || "0"}
                  </Typography>
                ),
              },
              {
                id: "registrationType",
                label: "Registration Type",
                format: (_, payment) => payment.registrationType || "N/A",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "paid"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "tournament",
                label: "Tournament",
                format: (_, payment) =>
                  payment?.tournament?.title || payment.tournamentTitle ? (
                    <Link
                      to={`/tournaments/${
                        payment?.tournament?.title || payment.tournamentTitle
                      }`}
                      style={{ textDecoration: "none", color: "inherit" }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          textTransform: "capitalize",
                          fontWeight: "medium",
                          textWrap: "balance",
                          fontSize: "16px",
                        }}
                      >
                        {(payment?.tournament?.title || payment.tournamentTitle)
                          .toLowerCase()
                          .replace(/-/g, " ")}
                      </Typography>
                    </Link>
                  ) : (
                    "N/A"
                  ),
              },
            ]}
            data={clubPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}
            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}

      {/* Player Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "player") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Player Payments
          </Typography>
          <DynamicTable
            columns={[
              {
                id: "playerName",
                label: "Player Name",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: "medium", fontSize: "16px" }}
                  >
                    {payment.playerName || "Unknown Player"}
                  </Typography>
                ),
              },
              {
                id: "cbid",
                label: "CBID",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/players/${payment.player?.cbid}`}
                    sx={{ color: "#1976d2", textDecoration: "none" }}
                  >
                    {payment.cbid || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "Transaction ID",
                format: (_, payment) => payment.paymentTransactionId || "N/A",
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "paymentMethod",
                label: "Payment Method",
                format: (_, payment) => payment.paymentMode || "Online",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "paid"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "tournament",
                label: "Tournament",
                format: (_, payment) =>
                  payment?.tournament?.title || payment.tournamentTitle ? (
                    <Link
                      to={`/tournaments/${
                        payment?.tournament?.title || payment.tournamentTitle
                      }`}
                      style={{ textDecoration: "none", color: "inherit" }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          textTransform: "capitalize",
                          fontWeight: "medium",
                          textWrap: "balance",
                          fontSize: "16px",
                        }}
                      >
                        {(payment?.tournament?.title || payment.tournamentTitle)
                          .toLowerCase()
                          .replace(/-/g, " ")}
                      </Typography>
                    </Link>
                  ) : (
                    "N/A"
                  ),
              },
            ]}
            data={playerPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}
            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}
    </Container>
  );
};

export default PaymentTournamentDetails;

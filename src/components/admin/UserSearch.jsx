import React from "react";
import {
  <PERSON><PERSON>ield,
  MenuItem,
  Box,
  Typography,
  Stack,
  Button,
  IconButton,
} from "@mui/material";
import RestartAltIcon from "@mui/icons-material/RestartAlt";

const availability = [
  { label: "Updated", value: true },
  { label: "Not Updated", value: false },
];

const roles = [
  { label: "Club", value: "club" },
  { label: "Player", value: "player" },
  { label: "Arbiter", value: "arbiter" },
];

const UserSearch = ({ value, onChange, onSearch, onReset }) => {
  return (
    <Box mb={3} p={2} sx={{ background: "#f9f9f9", borderRadius: 5 }}>
      <Typography mb={1} variant="h6" color="#3f51b5">
        Search User
      </Typography>
      <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
        <Box flex={1} minWidth={200}>
          <Typography variant="body1" gutterBottom>
            Select User Role
          </Typography>
          <TextField
            select
            value={value.role}
            name="role"
            onChange={onChange}
            fullWidth
            variant="outlined"
            size="small"
          >
            {roles.map((role) => (
              <MenuItem key={role.value} value={role.value}>
                {role.label}
              </MenuItem>
            ))}
          </TextField>
        </Box>

        <Box flex={1} minWidth={200}>
          <Typography variant="body1" gutterBottom>
            User Name
          </Typography>
          <TextField
            name="UserName"
            value={value.UserName}
            onChange={onChange}
            fullWidth
            variant="outlined"
            size="small"
            placeholder="Enter User name"
          />
        </Box>

        <Box flex={1} minWidth={200}>
          <Typography variant="body1" gutterBottom>
            Profile Status
          </Typography>
          <TextField
            select
            value={value.availability}
            name="availability"
            onChange={onChange}
            fullWidth
            variant="outlined"
            size="small"
          >
            {availability.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
        </Box>

        <Stack direction="row" alignItems="end" spacing={1} mt={3}>
          <IconButton color="secondary" onClick={onReset}>
            <RestartAltIcon />
          </IconButton>
          <Button variant="contained" onClick={onSearch}>
            Search
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default UserSearch;

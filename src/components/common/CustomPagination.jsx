import React from "react";
import { Box, Button, Typography, IconButton, useTheme } from "@mui/material";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";

// PageButton component for better control over page number buttons
const PageButton = ({ page, currentPage, onClick, theme }) => {
  const isCurrentPage = page === currentPage;

  const handleClick = () => {
    if (!isCurrentPage) {
      onClick();
    }
  };

  return (
    <Button
      variant="text"
      onClick={handleClick}
      disableRipple={isCurrentPage}
      tabIndex={isCurrentPage ? -1 : 0}
      aria-current={isCurrentPage ? "page" : undefined}
      aria-disabled={isCurrentPage}
      sx={{
        minWidth: "40px",
        height: "40px",
        margin: theme.spacing(0, 0.5),
        backgroundColor: isCurrentPage ? "#f5f5f5" : "transparent",
        color: "#000",
        fontWeight: isCurrentPage ? "medium" : "regular",
        borderRadius: "10px",
        border: "1px solid #00000030",
        cursor: isCurrentPage ? "default" : "pointer",
        pointerEvents: isCurrentPage ? "none" : "auto",
        "&:hover": {
          backgroundColor: isCurrentPage ? "#f5f5f5" : "rgba(0, 0, 0, 0.04)",
        },
      }}
    >
      <Typography
        variant="body1"
        sx={{
          fontSize: "16px",
          fontWeight: isCurrentPage ? "medium" : "regular",
        }}
      >
        {page}
      </Typography>
    </Button>
  );
};

const CustomPagination = ({ totalPages, currentPage, onPageChange, sx }) => {
  const theme = useTheme();

  // Ensure totalPages is at least 1 and is a number
  const validTotalPages = Math.max(1, isNaN(totalPages) ? 1 : totalPages);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= validTotalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  // Calculate visible page numbers
  const getVisiblePageNumbers = () => {
    let startPage = Math.max(1, currentPage - 1);
    let endPage = Math.min(validTotalPages, startPage + 3);

    if (endPage - startPage < 3) {
      startPage = Math.max(1, endPage - 3);
    }

    const pages = [];
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  const visiblePageNumbers = getVisiblePageNumbers();

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: theme.spacing(2, 0),
     
        ...sx,
      }}
    >
      <IconButton
        onClick={() => {
          handlePageChange(currentPage - 1);
        }}
        disabled={currentPage <= 1}
        sx={{
          mr: 1,
          color: "#000",
          "&.Mui-disabled": {
            color: "#bdbdbd",
          },
        }}
        aria-label="Previous page"
      >
        <ChevronLeftIcon />
      </IconButton>

      {visiblePageNumbers.map((page) => (
        <PageButton
          key={page}
          page={page}
          currentPage={currentPage}
          onClick={() => {
            handlePageChange(page);
          }}
          theme={theme}
        />
      ))}

      {validTotalPages > visiblePageNumbers[visiblePageNumbers.length - 1] && (
        <Typography sx={{ mx: 1 }}>...</Typography>
      )}

      <IconButton
        onClick={() => {
          handlePageChange(currentPage + 1);
        }}
        disabled={currentPage >= validTotalPages}
        sx={{
          ml: 1,
          color: "#000",
          "&.Mui-disabled": {
            color: "#bdbdbd",
          },
        }}
        aria-label="Next page"
      >
        <ChevronRightIcon />
      </IconButton>
    </Box>
  );
};

export default CustomPagination;

import { Document, Page, Text, View, StyleSheet } from "@react-pdf/renderer";
import { Client } from "../../api/client";
import { pdf } from "@react-pdf/renderer";
import { capitalizeWords, formatDateToDMY,upperCaseWords } from "../../utils/formatters";

import { saveAs } from "file-saver";
// Error Boundary Component for PDF debugging

// Tournament Receipt PDF styles - Table format like original
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontFamily: "Helvetica",
    position: "relative",
  },

  // Header section
  header: {
    alignItems: "center",
    marginBottom: 20,
  },
  watermark: {
    position: "absolute",
    top: "25%",
    left: "25%",
    transform: "translate(-50%, -50%) rotate(-45deg)",
    fontSize: 120,
    color: "rgba(43, 255, 1, 0.1)",
    fontWeight: "bold",
    zIndex: -1,
  },

  websiteTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 8,
    textAlign: "center",
  },

  receiptTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#000000",
    textAlign: "center",
    marginBottom: 15,
  },

  // Section styles
  section: {
    marginBottom: 15,
  },

  sectionTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 8,
    backgroundColor: "rgba(217, 217, 217, 0.5)",
    padding: 5,
  },

  // Table-like layout
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomStyle: "solid",
    borderBottomColor: "#cccccc",
    paddingVertical: 4,
    minHeight: 20,
  },

  labelColumn: {
    width: "45%",
    fontSize: 10,
    color: "#000000",
    paddingRight: 10,
  },

  valueColumn: {
    width: "55%",
    fontSize: 10,
    color: "#000000",
    paddingLeft: 10,
  },

  // Payment status highlighting
  paymentStatusRow: {
    flexDirection: "row",
    backgroundColor: "#e6f3ff",
    paddingVertical: 6,
    paddingHorizontal: 10,
    marginVertical: 5,
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: "#cccccc",
  },

  paymentStatusLabel: {
    width: "45%",
    fontSize: 10,
    color: "#000000",
    fontWeight: "bold",
  },

  paymentStatusValue: {
    width: "55%",
    fontSize: 10,
    color: "#000000",
    fontWeight: "bold",
  },

  // Special formatting for certain fields
  transactionRow: {
    flexDirection: "row",
    backgroundColor: "#f9f9f9",
    paddingVertical: 4,
    paddingHorizontal: 1,
    borderBottomWidth: 1,
    borderBottomStyle: "solid",
    borderBottomColor: "#cccccc",
  },

  // Footer section
  footer: {
    marginTop: 30,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopStyle: "solid",
    borderTopColor: "#cccccc",
  },

  registrationIdText: {
    fontSize: 10,
    color: "#000000",
    textAlign: "center",
    marginBottom: 10,
  },

  footerText: {
    fontSize: 9,
    color: "#000000",
    textAlign: "center",
    marginBottom: 3,
  },

  copyright: {
    fontSize: 8,
    color: "#666666",
    textAlign: "center",
    marginTop: 5,
  },

  generatedDate: {
    fontSize: 8,
    color: "#666666",
    textAlign: "right",
    marginTop: 10,
  },
});

// Tournament Receipt PDF Document - Table format matching original
const PaymentReceiptTemplate = ({ data = {} }) => {
  console.log(data);
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.watermark}>PAID</Text>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.websiteTitle}>ChessBrigade.com</Text>
          <Text style={styles.receiptTitle}>
            TOURNAMENT REGISTRATION RECEIPT
          </Text>
        </View>

        {/* Player Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Player Information</Text>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Full Name:</Text>
            <Text style={styles.valueColumn}>{capitalizeWords(data.playerName)}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Contact Email:</Text>
            <Text style={styles.valueColumn}>{data.playerEmail}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Contact Phone:</Text>
            <Text style={styles.valueColumn}>{data.playerPhoneNumber}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Player ID:</Text>
            <Text style={styles.valueColumn}>{data.playerId}</Text>
          </View>
        </View>

        {/* Tournament Details Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tournament Details</Text>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Name:</Text>
            <Text style={styles.valueColumn}>{data.tournamentTitle.toUpperCase().replace(/-/g, " ")}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Start Date:</Text>
            <Text style={styles.valueColumn}>{formatDateToDMY(data.tournamentStartDate)}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Organizer Name:</Text>
            <Text style={styles.valueColumn}>
              {data.tournamentOrganizerName}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Organizer Email:</Text>
            <Text style={styles.valueColumn}>
              {data.tournamentOrganizerEmail}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>
              Tournament Organizer PhoneNumber:
            </Text>
            <Text style={styles.valueColumn}>
              {data.tournamentOrganizerPhoneNumber}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Location:</Text>
            <Text style={styles.valueColumn}>
              {data.tournamentVenueAddress}
            </Text>
          </View>
        </View>

        {/* Payment Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Information</Text>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Registration ID:</Text>
            <Text style={styles.valueColumn}>{upperCaseWords(data.registrationId)}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Registration Fee:</Text>
            <Text style={styles.valueColumn}>{data.paymentAmount}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Payment Method:</Text>
            <Text style={styles.valueColumn}>{upperCaseWords(data.paymentMethod)}</Text>
          </View>

          <View style={styles.transactionRow}>
            <Text style={styles.labelColumn}>Transaction ID:</Text>
            <Text style={styles.valueColumn}>{data.paymentTransactionId}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Payment Date:</Text>
            <Text style={styles.valueColumn}>{formatDateToDMY(data.paymentDate)}</Text>
          </View>

          <View style={styles.paymentStatusRow}>
            <Text style={styles.paymentStatusLabel}>Payment Status:</Text>
            <Text style={styles.paymentStatusValue}>{upperCaseWords(data.paymentStatus)}</Text>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            For any queries, contact tournament Organizer.
          </Text>
          <Text style={styles.copyright}>
            © 2025 ChessBrigade.com. All rights reserved.
          </Text>
          <Text style={styles.generatedDate}>{data.timestamp}</Text>
        </View>
      </Page>
    </Document>
  );
};

export const generatePdfAndDownload = async ({toast,txnId}) => {
  try {
    const encodedTxnId = encodeURIComponent(txnId);
    const url = `/payment/receipt?txnId=${encodedTxnId}`;

    // Step 1: Fetch data from backend (returns JSON, not blob)
    const response = await Client.get(url);

    if (!response.data.success || !response.data.data) {
      toast.error("Failed to fetch receipt data");
      return;
    }

    // Step 2: Generate PDF using @react-pdf/renderer
    const pdfBlob = await pdf(
      <PaymentReceiptTemplate data={response.data.data} />
    ).toBlob();

    // Step 3: Trigger download
    saveAs(pdfBlob, `payment_receipt_${txnId}.pdf`);
  } catch (error) {
    console.error("Receipt generation failed:", error);
    toast.error("Failed to generate receipt PDF");
  }
};

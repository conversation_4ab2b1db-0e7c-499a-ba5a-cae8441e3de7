import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
  IconButton,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Checkbox,
  FormControlLabel,
  Chip,
  Card,
  CardContent,
  useTheme,
  alpha,
} from "@mui/material";
import {
  Close as CloseIcon,
  SportsTennis as TournamentIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  EmojiEvents as TrophyIcon,
} from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

/**
 * Modal for configuring tournament rounds by selecting age categories
 */
const TournamentRoundsModal = ({ open, onClose, title }) => {
  const theme = useTheme();
  const [selectedCategories, setSelectedCategories] = useState({
    maleAgeCategory: {},
    femaleAgeCategory: {},
  });
  const [roundsConfig, setRoundsConfig] = useState({
    maleAgeCategory: {},
    femaleAgeCategory: {},
  });
  const [loading, setLoading] = useState(false);
  const toast = UseToast();
  const [tournament, setTournament] = useState({});

  useEffect(() => {
    if (!open || !title) return;
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${title}`);
        if (response.data.success) {
          setTournament(response.data.data);
        } else {
          toast.error("Failed to fetch tournament details");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error("Error fetching tournament details");
      } finally {
        setLoading(false);
      }
    };
    fetchTournamentDetails();
  }, [title, open]);

  // Fixed useEffect to properly initialize state after tournament data is loaded
  useEffect(() => {
    if (open && tournament && Object.keys(tournament).length > 0) {
      

      const initSelected = {
        maleAgeCategory: {},
        femaleAgeCategory: {},
      };
      const initRounds = {
        maleAgeCategory: {},
        femaleAgeCategory: {},
      };

      // Initialize male categories
      if (
        tournament.maleAgeCategory &&
        Array.isArray(tournament.maleAgeCategory)
      ) {
        tournament.maleAgeCategory.forEach((category) => {
          // Check if this category has rounds configured
          const hasRounds =
            tournament.numberOfRounds?.maleAgeCategory &&
            tournament.numberOfRounds?.maleAgeCategory[category] !== undefined;
          initSelected.maleAgeCategory[category] = hasRounds;

          if (hasRounds) {
            initRounds.maleAgeCategory[category] =
              tournament.numberOfRounds.maleAgeCategory[category];
          }
        });
      }

      // Initialize female categories
      if (
        tournament.femaleAgeCategory &&
        Array.isArray(tournament.femaleAgeCategory)
      ) {
        tournament.femaleAgeCategory.forEach((category) => {
          // Check if this category has rounds configured
          const hasRounds =
            tournament.numberOfRounds?.femaleAgeCategory &&
            tournament.numberOfRounds?.femaleAgeCategory[category] !==
              undefined;
          initSelected.femaleAgeCategory[category] = hasRounds;

          if (hasRounds) {
            initRounds.femaleAgeCategory[category] =
              tournament.numberOfRounds.femaleAgeCategory[category];
          }
        });
      }

    

      setSelectedCategories(initSelected);
      setRoundsConfig(initRounds);
    }
  }, [open, tournament]);

  // Handle category selection
  const handleCategorySelect = (gender, category, checked) => {
    setSelectedCategories((prev) => ({
      ...prev,
      [gender]: {
        ...prev[gender],
        [category]: checked,
      },
    }));

    if (!checked) {
      // If unchecked, remove from rounds config
      setRoundsConfig((prev) => {
        const newConfig = { ...prev };
        if (newConfig[gender]) {
          delete newConfig[gender][category];
        }
        return newConfig;
      });
    } else {
      // If checked, use existing round value or default to 1
      const existingRounds =
        gender === "maleAgeCategory"
          ? tournament.numberOfRounds?.maleAgeCategory?.[category]
          : tournament.numberOfRounds?.femaleAgeCategory?.[category];

      setRoundsConfig((prev) => ({
        ...prev,
        [gender]: {
          ...prev[gender],
          [category]: existingRounds || 1,
        },
      }));
    }
  };

  // Handle rounds input change
  const handleRoundsChange = (gender, category, value) => {
    const numValue = parseInt(value) || 1;
    if (numValue < 1 || numValue > 50) return;

    setRoundsConfig((prev) => ({
      ...prev,
      [gender]: {
        ...prev[gender],
        [category]: numValue,
      },
    }));
  };

  // Handle saving configuration
  const handleSaveConfiguration = async () => {
    if (!tournament?.id) {
      toast.error("Tournament information is missing");
      return;
    }

    // Check if at least one category is selected
    const hasSelectedCategories =
      Object.values(selectedCategories.maleAgeCategory || {}).some(Boolean) ||
      Object.values(selectedCategories.femaleAgeCategory || {}).some(Boolean);

    if (!hasSelectedCategories) {
      toast.error("Please select at least one category");
      return;
    }

    setLoading(true);
    try {
      const newTitle = encodeURIComponent(tournament.title);

      // Prepare the data to send
      const updateData = {
        maleAgeCategory: roundsConfig.maleAgeCategory || {},
        femaleAgeCategory: roundsConfig.femaleAgeCategory || {},
      };

     

      const response = await Client.patch(
        `/tournament/${newTitle}/rounds`,
        updateData
      );

      if (response.data.success) {
        toast.success("Tournament rounds configuration updated successfully");
        onClose();
      } else {
        toast.error(
          response.data.message || "Failed to update tournament configuration"
        );
      }
    } catch (error) {
      console.error("Error updating tournament rounds:", error);
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error(
          "An error occurred while updating tournament configuration"
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const renderCategorySection = (gender, categories, title, icon, color) => {
    if (!categories || categories.length === 0) return null;

    return (
      <Card
        elevation={0}
        sx={{
          mb: 3,
          border: `2px solid ${alpha(color, 0.2)}`,
          borderRadius: 3,
          overflow: "visible",
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: 30,
                height: 30,
                borderRadius: 2,
                backgroundColor: alpha(color, 0.1),
                color: color,
                mr: 2,
              }}
            >
              {icon}
            </Box>
            <Typography
              variant="h6"
              fontWeight="600"
              color={color}
              sx={{ fontSize: "1rem" }}
            >
              {title}
            </Typography>
          </Box>

          <Grid container spacing={2}>
            {categories.map((category) => {
              const isSelected =
                selectedCategories[gender]?.[category] || false;
              const currentRounds = roundsConfig[gender]?.[category] || 1;

              return (
                <Grid item xs={12} sm={6} md={4} key={category}>
                  <Card
                    elevation={isSelected ? 4 : 1}
                    sx={{
                      border: isSelected
                        ? `2px solid ${color}`
                        : `1px solid ${alpha(theme.palette.grey[400], 0.3)}`,
                      borderRadius: 2,
                      transition: "all 0.3s ease",
                      transform: isSelected ? "translateY(-2px)" : "none",
                      backgroundColor: isSelected
                        ? alpha(color, 0.02)
                        : "white",
                      "&:hover": {
                        transform: "translateY(-2px)",
                        boxShadow: theme.shadows[8],
                      },
                    }}
                  >
                    <CardContent sx={{ p: 2.5 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={isSelected}
                            onChange={(e) =>
                              handleCategorySelect(
                                gender,
                                category,
                                e.target.checked
                              )
                            }
                            disabled={loading}
                            sx={{
                              color: alpha(color, 0.6),
                              "&.Mui-checked": {
                                color: color,
                              },
                            }}
                          />
                        }
                        label={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              flexWrap: "wrap",
                            }}
                          >
                            <Typography
                              variant="subtitle1"
                              sx={{ fontSize: "1.2rem" }}
                              fontWeight="500"
                            >
                              {category}
                            </Typography>
                            {isSelected && (
                              <Chip
                                size="small"
                                label={`${currentRounds} Round${
                                  currentRounds > 1 ? "s" : ""
                                }`}
                                sx={{
                                  ml: 1,
                                  backgroundColor: alpha(color, 0.1),
                                  color: color,
                                  fontWeight: 500,
                                  fontSize: "0.75rem",
                                }}
                              />
                            )}
                          </Box>
                        }
                        sx={{ mb: isSelected ? 2 : 0, width: "100%" }}
                      />

                      {isSelected && (
                        <Box
                          sx={{
                            animation: "fadeIn 0.3s ease-in",
                            "@keyframes fadeIn": {
                              "0%": {
                                opacity: 0,
                                transform: "translateY(-10px)",
                              },
                              "100%": {
                                opacity: 1,
                                transform: "translateY(0)",
                              },
                            },
                          }}
                        >
                          <TextField
                            label="Number of Rounds"
                            type="number"
                            size="small"
                            fullWidth
                            inputProps={{ min: 1, max: 50 }}
                            value={currentRounds}
                            onChange={(e) =>
                              handleRoundsChange(
                                gender,
                                category,
                                e.target.value
                              )
                            }
                            disabled={loading}
                            variant="outlined"
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                "&.Mui-focused fieldset": {
                                  borderColor: color,
                                },
                              },
                              "& .MuiInputLabel-root.Mui-focused": {
                                color: color,
                              },
                            }}
                          />
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  const getSelectedCount = () => {
    const maleCount = Object.values(
      selectedCategories.maleAgeCategory || {}
    ).filter(Boolean).length;
    const femaleCount = Object.values(
      selectedCategories.femaleAgeCategory || {}
    ).filter(Boolean).length;
    return maleCount + femaleCount;
  };

  return (
    <Dialog
      open={open}
      onClose={loading ? null : onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          boxShadow: "0 24px 48px rgba(0,0,0,0.15)",
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          background: `linear-gradient(135deg, ${alpha(
            theme.palette.primary.main,
            0.1
          )} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 3,
          px: 4,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: 56,
              height: 56,
              borderRadius: 3,
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main,
              mr: 2,
            }}
          >
            <TrophyIcon fontSize="large" />
          </Box>
          <Box>
            <Typography
              variant="h5"
              component="div"
              fontWeight="700"
              color="primary"
            >
              Configure Rounds
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              {tournament?.title}
            </Typography>
          </Box>
        </Box>
        <IconButton
          edge="end"
          onClick={onClose}
          disabled={loading}
          aria-label="close"
          sx={{
            backgroundColor: alpha(theme.palette.grey[500], 0.1),
            "&:hover": {
              backgroundColor: alpha(theme.palette.grey[500], 0.2),
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 4, py: 3 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="body1" color="text.secondary" paragraph>
            Select age categories and configure the number of rounds for your
            tournament
          </Typography>

          {getSelectedCount() > 0 && (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                p: 2,
                backgroundColor: alpha(theme.palette.success.main, 0.1),
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
              }}
            >
              <TrophyIcon
                sx={{ color: theme.palette.success.main, mr: 1, fontSize: 16 }}
              />
              <Typography variant="body2" color="success.main" fontWeight="500">
                {getSelectedCount()} category(ies) selected for tournament
              </Typography>
            </Box>
          )}
        </Box>

        {loading && (
          <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {!loading && (
          <>
            {renderCategorySection(
              "maleAgeCategory",
              tournament?.maleAgeCategory,
              "Male Categories",
              <MaleIcon fontSize="large" />,
              theme.palette.info.main
            )}

            {renderCategorySection(
              "femaleAgeCategory",
              tournament?.femaleAgeCategory,
              "Female Categories",
              <FemaleIcon fontSize="large" />,
              theme.palette.secondary.main
            )}

            {!tournament?.maleAgeCategory?.length &&
              !tournament?.femaleAgeCategory?.length && (
                <Box
                  sx={{
                    textAlign: "center",
                    py: 8,
                    backgroundColor: alpha(theme.palette.grey[100], 0.5),
                    borderRadius: 3,
                    border: `2px dashed ${alpha(theme.palette.grey[400], 0.3)}`,
                  }}
                >
                  <TournamentIcon
                    sx={{ fontSize: 64, color: theme.palette.grey[400], mb: 2 }}
                  />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No Categories Available
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Please add age categories to this tournament first
                  </Typography>
                </Box>
              )}
          </>
        )}
      </DialogContent>

      <DialogActions
        sx={{
          px: 4,
          py: 3,
          backgroundColor: alpha(theme.palette.grey[50], 0.5),
          gap: 2,
        }}
      >
        <Button
          onClick={onClose}
          disabled={loading}
          size="large"
          sx={{
            borderRadius: 3,
            px: 4,
            py: 1.5,
            textTransform: "none",
            fontWeight: 500,
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSaveConfiguration}
          variant="contained"
          disabled={loading || getSelectedCount() === 0}
          size="large"
          startIcon={
            loading ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              <TrophyIcon />
            )
          }
          sx={{
            borderRadius: 3,
            px: 4,
            py: 1.5,
            textTransform: "none",
            fontWeight: 600,
            boxShadow: theme.shadows[8],
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            "&:hover": {
              boxShadow: theme.shadows[12],
              transform: "translateY(-1px)",
            },
            "&:disabled": {
              background: theme.palette.grey[300],
              color: theme.palette.grey[500],
            },
          }}
        >
          {loading ? "Saving Rounds..." : "Save Rounds"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TournamentRoundsModal;

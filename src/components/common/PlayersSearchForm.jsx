import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Button,
  Grid,
  MenuItem,
  Paper,
  Select,
  TextField,
  CircularProgress,
  Typography,
  Box,
} from "@mui/material";
import {
  ArrowCircleDownRounded,
  RestartAlt,
  Search as SearchIcon,
} from "@mui/icons-material";

import { Client } from "../../api/client";
import useUserGeoInfo from "../../lib/hooks/UseGetlocation";

const PlayersSearchForm = ({
  search,
  setSearch,
  handleSearch,
  loading,
  handleReset,
  mobileEmail = false,
}) => {
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [cities, setCities] = useState([]);
  const [locationLoaded, setLocationLoaded] = useState(false);
  const { geoInfo } = useUserGeoInfo();

  // Memoize API calls and handlers
  const memoizedHandleSearch = useCallback(
    (page) => {
      handleSearch(page);
    },
    [handleSearch]
  );

  const fetchCountries = useCallback(async () => {
    const response = await Client.get("/location/countries");
    if (!response.data.success) return;
    setCountries(response.data.data);
  }, []);

  const fetchStates = useCallback(async (countryCode) => {
    const response = await Client.get(`/location/states/${countryCode}`, {
      params: { country: countryCode },
    });
    if (!response.data.success) return;
    setStates(response.data.data);
    return response.data.data;
  }, []);

  const fetchDistricts = useCallback(async (countryCode, stateCode) => {
    const response = await Client.get(`/location/districts/${stateCode}`, {
      params: { country: countryCode, state: stateCode },
    });
    if (!response.data.success) return;
    setDistricts(response.data.data);
    return response.data.data;
  }, []);

  const fetchCities = useCallback(async (countryCode, stateCode) => {
    const response = await Client.get(
      `/location/cities/${countryCode}/${stateCode}`,
      {
        params: { country: countryCode, state: stateCode },
      }
    );
    if (!response.data.success) return;
    setCities(response.data.data);
    return response.data.data;
  }, []);

  // Load countries on component mount - using memoized fetch
  useEffect(() => {
    fetchCountries();
  }, [fetchCountries]);

  // Memoize country and state objects to avoid recalculations
  const countryObj = useMemo(
    () => countries.find((c) => c.name === search.country),
    [countries, search.country]
  );

  const stateObj = useMemo(
    () => states.find((s) => s.name === search.state),
    [states, search.state]
  );

  // Handle geoInfo changes - set location data but don't trigger search yet
  useEffect(() => {
    const setupLocationData = async () => {
      if (!geoInfo || countries.length === 0) return;

      const countryObj = countries.find((c) => c.isoCode === geoInfo.country);
      if (!countryObj) return;

      // Prepare a single state update to batch changes
      let locationUpdate = { country: countryObj.name };

      // Load and set states
      const stateRes = await fetchStates(countryObj.isoCode);
      if (!stateRes) return;

      const stateObj = stateRes.find((s) => s.name === geoInfo.state);
      if (stateObj) {
        locationUpdate.state = stateObj.name;

        // // Load cities and districts (if in India) concurrently
        // const promises = [fetchCities(countryObj.isoCode, stateObj.isoCode)];

        // if (countryObj.name === "India") {
        //   promises.push(fetchDistricts(countryObj.isoCode, stateObj.isoCode));
        // }

        // const [cities, districts] = await Promise.all(promises);

        // if (cities) {
        //   const cityObj = cities.find((c) => c.name === geoInfo.city);
        //   if (cityObj) {
        //     locationUpdate.city = cityObj.name;
        //   }
        // }
      }

      // Apply all location updates in one batch
      setSearch((prev) => ({ ...prev, ...locationUpdate }));

      // Mark location data as loaded - will trigger search in next effect
      setLocationLoaded(true);
    };

    setupLocationData();
  }, [geoInfo, countries, fetchStates, fetchCities, fetchDistricts, setSearch]);

  // Trigger search only when location is fully loaded
  useEffect(() => {
    if (locationLoaded) {
      memoizedHandleSearch(1);
      // Reset flag to prevent repeated searches
      setLocationLoaded(false);
    }
  }, [locationLoaded, memoizedHandleSearch]);

  // Load states when country changes
  useEffect(() => {
    if (search.country && countryObj) {
      fetchStates(countryObj.isoCode);
    } else {
      setStates([]);
      // Reset dependent fields in one batch
      setSearch((prev) => ({ ...prev, state: "", district: "", city: "" }));
    }
  }, [search.country, countryObj, fetchStates, setSearch]);

  // Load districts when state changes
  useEffect(() => {
    if (search.country === "India" && countryObj && stateObj) {
      fetchDistricts(countryObj.isoCode, stateObj.isoCode);
    } else {
      setDistricts([]);
      // Reset dependent field
      setSearch((prev) => ({ ...prev, district: "" }));
    }
  }, [search.country, countryObj, stateObj, fetchDistricts, setSearch]);

  // Load cities when state changes
  useEffect(() => {
    if (countryObj && stateObj) {
      fetchCities(countryObj.isoCode, stateObj.isoCode);
    } else {
      setCities([]);
      // Reset dependent field
      setSearch((prev) => ({ ...prev, city: "" }));
    }
  }, [countryObj, stateObj, fetchCities, setSearch]);

  // Handle input changes without triggering search
  const handleInputChange = (field, value) => {
    setSearch((prev) => ({ ...prev, [field]: value }));
  };

  // Handle key press in input fields (for Enter key)
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSearch();
    }
    let name = e.target.name;
    if (
      name === "mobile" &&
      (!/[0-9]/.test(e.key) || e.target.value.length >= 10)
    ) {
      e.preventDefault();
    }
    if (name === "mail") {
      if (!/[a-zA-Z0-9@._-]/.test(e.key)) {
        e.preventDefault();
      }
    }
  };

  return (
    <Paper sx={{ mb: 3, p: 2, bgcolor: "#f9f9f9" }}>
      <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
        <Typography variant="h5" sx={{ color: "#3f51b5" }}>
          Search Players
        </Typography>
      </Box>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Player Name"
            variant="outlined"
            name="playerName"
            fullWidth
            size="small"
            value={search.playerName}
            onChange={(e) => handleInputChange("playerName", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Player ID"
            variant="outlined"
            name="playerId"
            fullWidth
            size="small"
            value={search.playerId}
            onChange={(e) => handleInputChange("playerId", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid>
        {mobileEmail && (
          <>
            <Grid item xs={12} sm={4} md={3} lg={2}>
              <TextField
                placeholder="Phone Number"
                variant="outlined"
                name="mobile"
                fullWidth
                size="small"
                value={search.mobile}
                onChange={(e) => handleInputChange("mobile", e.target.value)}
                onKeyPress={handleKeyPress}
                sx={{ bgcolor: "white" }}
              />
            </Grid>

            <Grid item xs={12} sm={4} md={3} lg={2}>
              <TextField
                placeholder="E mail"
                variant="outlined"
                name="email"
                fullWidth
                type="mail"
                size="small"
                value={search.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                onKeyPress={handleKeyPress}
                sx={{ bgcolor: "white" }}
              />
            </Grid>
          </>
        )}
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.country}
            onChange={(e) => handleInputChange("country", e.target.value)}
            renderValue={(selected) => (selected ? selected : "Country")}
            sx={{ bgcolor: "white" }}
          >
            <MenuItem value="">
               Country 
            </MenuItem>
            {countries.map((country) => (
              <MenuItem key={country.isoCode} value={country.name}>
                {country.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.state}
            onChange={(e) => handleInputChange("state", e.target.value)}
            renderValue={(selected) => (selected ? selected : "State")}
            sx={{ bgcolor: "white" }}
            disabled={!search.country}
          >
            <MenuItem value="">
               State 
            </MenuItem>
            {states.map((state) => (
              <MenuItem key={state.isoCode} value={state.name}>
                {state.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        {search.country === "India" && (
          <Grid item xs={12} sm={4} md={3} lg={2}>
            <Select
              fullWidth
              displayEmpty
              size="small"
              value={search.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              renderValue={(selected) => (selected ? selected : "District")}
              sx={{ bgcolor: "white" }}
              disabled={!search.state}
            >
              <MenuItem value="">
                 district 
              </MenuItem>
              {districts.map((district) => (
                <MenuItem
                  key={district.id || district.name}
                  value={district.name}
                >
                  {district.name}
                </MenuItem>
              ))}
            </Select>
          </Grid>
        )}
        {(search.country !== "India" || search.country === "") && (
          <Grid item xs={12} sm={4} md={3} lg={2}>
            <TextField
              placeholder="District"
              variant="outlined"
              name="district"
              fullWidth
              size="small"
              value={search.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              onKeyPress={handleKeyPress}
              sx={{ bgcolor: "white" }}
              disabled={!search.state}
            />
          </Grid>
        )}

        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.city}
            onChange={(e) => handleInputChange("city", e.target.value)}
            renderValue={(selected) => (selected ? selected : "City")}
            sx={{ bgcolor: "white" }}
            disabled={!search.state}
          >
            <MenuItem value="">
               City 
            </MenuItem>
            {cities.map((city) => (
              <MenuItem key={city.id || city.name} value={city.name}>
                {city.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>

        <Grid item xs style={{ flexGrow: 1 }} />

        <Grid
          item
          xs={12}
          sm={4}
          md={3}
          lg={2}
          sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}
        >
          <Button
            variant="containedSecondary"
            color="secondary"
            sx={{
              width: "40px",
              minWidth: "40px !important",
            }}
            onClick={handleReset}
            disabled={loading}
          >
            <RestartAlt />
          </Button>
          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={handleSearch}
            disabled={loading}
            startIcon={
              loading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SearchIcon />
              )
            }
            sx={{
              bgcolor: "#3f51b5",
              textTransform: "none",
              height: "40px",
              fontSize: "16px",
              maxWidth: {
                xs: "100%",
                sm: "150px",
              },
            }}
          >
            Search
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PlayersSearchForm;

import React, { useEffect, useState } from "react";
import { DetailTable } from "./DetailTable";

import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  capitalizeFirstLetter,
  formatAgeCategory,
  formatDateToDMY,
  formatDecimal,
} from "../../utils/formatters";
import UseGobalContext from "../../lib/hooks/UseGlobalContext";
import { formatPhoneNumber } from "../../utils/formatters";

const TournamentDetailsView = ({ tournaments }) => {
 console.log("tournaments",tournaments)
  const [expanded, setExpanded] = useState({
    tournamentDetails: true,
    personalInfo: true,
    venueDetails: true,
    prizeDetails: true,
    miscDetails: true,
  });
  const { user } = UseGobalContext();
  const [tournamentData, setTournamentData] = useState({});
  useEffect(() => {
    if (!tournaments) return;
    setTournamentData(formatTournamentData(tournaments));
  }, [tournaments]);

  const handleAccordionChange = (panel) => (_, isExpanded) => {
    setExpanded({
      ...expanded,
      [panel]: isExpanded,
    });
  };
  const color =
    user?.role !== "club"
      ? { odd: "#F5F2FC", even: "#F2FDF9" }
      : { odd: "#F3BEB90D", even: "#F3BEB926" };

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        overflow: "hidden",
        mb: 4,
        backgroundColor: "#f5f9f6",
      }}
    >
      {tournamentData && (
        <TournamentHeader
          title={tournamentData.title || ""}
          presentedBy={tournamentData.presentedBy || ""}
          approvals={tournamentData.approvals || ""}
          subtitle={tournamentData.subtitle || ""}
          status={tournamentData.status || ""}
        />
      )}

      {tournamentData &&
        tournamentData.sections &&
        tournamentData.sections.map((section) => (
          <SectionAccordion
            key={section.id}
            title={section.title}
            expanded={expanded[section.id]}
            onChange={handleAccordionChange(section.id)}
          >
            <DetailTable details={section.details} rowColor={color} />
          </SectionAccordion>
        ))}
    </Paper>
  );
};

export default TournamentDetailsView;

// Format tournament data for display
function formatTournamentData(tournament) {
  if (!tournament) {
    return {
      title: "",
      subtitle: "",
      sections: [],
    };
  }

  function formatAmount(amount) {
    const num = parseFloat(amount);
    if (isNaN(num)) return amount;

    return new Intl.NumberFormat("en-IN").format(num);
  }

  return {
    title: tournament.title
      ? tournament?.title?.toUpperCase().replace(/-/g, " ")
      : "",
    subtitle: tournament?.subTitle
      ? capitalizeFirstLetter(tournament?.subTitle)
      : "",
    presentedBy: tournament?.presentedBy
      ? capitalizeFirstLetter(tournament?.presentedBy)
      : "",
    approvals: `${
      tournament.nationalApproval
        ? `National Approval ${tournament.nationalApproval}${
            tournament.stateApproval || tournament.districtApproval ? " | " : ""
          }`
        : ""
    }${
      tournament.stateApproval
        ? `State Approval ${tournament.stateApproval}${
            tournament.districtApproval ? " | " : ""
          }`
        : ""
    }${
      tournament.districtApproval
        ? `District Approval ${tournament.districtApproval}`
        : ""
    }`,
    status: (() => {
      try {
        const now = new Date();
        if (
          !tournament.registrationStartDate ||
          !tournament.registrationEndDate ||
          !tournament.startDate ||
          !tournament.endDate
        ) {
          return { name: "Unknown", color: "#757575" };
        }

        if (new Date(tournament.registrationStartDate) > now) {
          return { name: "Upcoming", color: "#4CAF50" };
        } else if (
          new Date(tournament.registrationStartDate) <= now &&
          new Date(tournament.registrationEndDate) > now
        ) {
          return { name: "Registration Open", color: "#2196F3" };
        } else if (
          new Date(tournament.registrationEndDate) <= now &&
          new Date(tournament.startDate) > now
        ) {
          return { name: "Registration Closed", color: "#F44336" };
        } else if (
          new Date(tournament.startDate) <= now &&
          new Date(tournament.endDate) > now
        ) {
          return { name: "Ongoing", color: "#FF9800" };
        } else {
          return { name: "Completed", color: "#757575" };
        }
      } catch (error) {
        console.error("Error determining tournament status:", error);
        return { name: "Unknown", color: "#757575" };
      }
    })(),
    sections: [
      {
        id: "tournamentDetails",
        title: "Tournament Details:",
        details: [
          {
            label: "Organizer Name",
            value: capitalizeFirstLetter(tournament.organizerName) || "-",
          },
          {
            label: "Tournament Level",
            value: capitalizeFirstLetter(tournament.tournamentLevel) || "-",
          },
          {
            label: "FIDE Rated",
            value: `${tournament.fideRated ? "Rated" : "Unrated"}`,
          },
          {
            label: "Tournament Date & Time",
            value:
              tournament.startDate && tournament.endDate
                ? `${formatDateToDMY(
                    tournament.startDate
                  )} to ${formatDateToDMY(tournament.endDate)} at ${
                    tournament.reportingTime
                  }`
                : "Dates -",
          },
          {
            label: "Tournament Category",
            value:
              capitalizeFirstLetter(
                tournament.id === 'd8084ed9-c0db-4063-a592-45ea24c96291'
                  ?  'Children': tournament.id === '294c9034-9a8b-40e1-bffe-b837e9f5ad73'? "Children & Open"
                  : tournament.tournamentCategory
              ) || "-",
          },
          {
            label: "Male Age Category",
            value: formatAgeCategory(tournament.maleAgeCategory) || "-",
          },
          {
            label: "Female Age Category",
            value: formatAgeCategory(tournament.femaleAgeCategory) || "-",
          },
          {
            label: "Registration Date & time",
            value:
              tournament.registrationStartDate &&
              tournament.registrationEndDate &&
              tournament.registrationEndTime
                ? `${formatDateToDMY(
                    tournament.registrationStartDate
                  )} to ${formatDateToDMY(tournament.registrationEndDate)} at ${
                    tournament.registrationEndTime
                  }`
                : "-",
          },

          {
            label: "Entry Fee",
            value: tournament.entryFee
              ? `${tournament.entryFeeCurrency} ${formatDecimal(
                  tournament.entryFee
                )}`
              : "-",
          },
          // { label: "Category", value: tournament.category },
          {
            label: "Time Control",
            value: tournament.timeControl
              ? `${capitalizeFirstLetter(tournament.timeControl)}${
                  tournament.timeControlDuration
                    ? ` - ${tournament.timeControlDuration}`
                    : ""
                }
                ${
                  tournament.timeControlIncrement &&
                  tournament.timeControlIncrement !== "0"
                    ? ` + ${tournament.timeControlIncrement} Increment`
                    : ""
                }
                `
              : "-",
          },
          {
            label: "Tournament System",
            value: tournament.tournamentSystem
              ? `${capitalizeFirstLetter(
                  tournament.tournamentSystem === "swiss-system"
                    ? "swiss System"
                    : tournament.tournamentSystem === "round-robin"
                    ? "Round Robin"
                    : tournament.tournamentSystem
                )}`
              : "-",
          },
          {
            label: "Tournament Type",
            value:
              tournament.tournamentType === "individual"
                ? "Individual"
                : tournament.tournamentType === "team"
                ? "Team"
                : "-",
          },
          {
            label: "No. of Rounds",
            value: tournament.numberOfRounds
              ? formatRoundsDisplay(tournament.numberOfRounds)
              : "Not specified",
          },
          {
            label: "Spot Entry",
            value: `${tournament.spotEntry ? "Yes" : "No"}`,
          },
        ],
      },
      {
        id: "personalInfo",
        title: "Personnel & Contact Info:",
        details: [
          {
            label: "Chief Arbiter Name",
            value: capitalizeFirstLetter(tournament?.arbiter?.name) || "-",
          },
          {
            label: "Tournament Director Name",
            value:
              capitalizeFirstLetter(tournament.tournamentDirectorName) || "-",
          },
          {
            label: "Contact Person Name",
            value: capitalizeFirstLetter(tournament.contactPersonName) || "-",
          },
          {
            label: "Contact Number",
            value: tournament.contactNumber
              ? formatPhoneNumber(tournament.contactNumber)
              : "-",
          },
          { label: "Email ID", value: tournament.email || "-" },
          {
            label: "Alternate Contact",
            value: tournament.alternateContactNumber
              ? formatPhoneNumber(tournament.alternateContactNumber)
              : "-",
          },
        ],
      },
      {
        id: "venueDetails",
        title: "Tournament Venue Details:",
        details: [
          {
            label: "Venue Address",
            value: tournament.venueAddress || "-",
          },
          {
            label: "Nearest Landmark",
            value: tournament.nearestLandmark || "-",
          },
          { label: "City", value: tournament.city || "-" },
          {
            label: "District",
            value: capitalizeFirstLetter(tournament.district) || "-",
          },
          { label: "Pincode", value: tournament.pincode || "-" },
          { label: "State", value: tournament.state || "-" },
          { label: "Country", value: tournament.country || "-" },
        ],
      },
      {
        id: "prizeDetails",
        title: "Tournament Prize Details:",
        details: [
          {
            label: "Number of Trophies for Male",
            value: tournament.numberOfTrophiesMale || "-",
          },
          {
            label: "Number of Trophies for Female",
            value: tournament.numberOfTrophiesFemale || "-",
          },
          {
            label: "Total Cash Prize",
            value:
              tournament.totalCashPrizeCurrency &&
              tournament.totalCashPrizeAmount
                ? `${tournament.totalCashPrizeCurrency} ${formatAmount(
                    tournament.totalCashPrizeAmount
                  )}`
                : "-",
          },
        ],
      },
      {
        id: "miscDetails",
        title: "Miscellaneous Details:",
        details: [
          {
            label: "Chess Board Provided",
            value: `${tournament.chessboardProvided == true ? "Yes" : "No"}`,
          },
          {
            label: "Timer Provided",
            value: `${tournament.timerProvided == true ? "Yes" : "No"}`,
          },
          {
            label: "Parking Facility",
            value: `${
              tournament.parkingFacility === "yes"
                ? "2 & 4 Wheeler"
                : tournament.parkingFacility === "limited"
                ? "2 Wheeler Only"
                : "Not Available"
            }`,
          },
          {
            label: "Canteen Facility",
            value: Array.isArray(tournament.foodFacility)
              ? tournament.foodFacility
                  .filter((value) => value !== "nil")
                  .join(", ")
                  .replace(/\b\w/g, (char) => char.toUpperCase()) ||
                "Not Available"
              : "Not Available",
          },
        ],
      },
    ],
  };
}

// Section Accordion Component
const SectionAccordion = ({ title, expanded, onChange, children }) => (
  <Accordion
    expanded={expanded}
    onChange={onChange}
    sx={{
      boxShadow: "none",
      "&:before": {
        display: "none",
        margin: "0 !important",
      },
      "&.MuiAccordion-root.Mui-expanded": {
        margin: "0 !important",
      },
    }}
  >
    <AccordionSummary
      expandIcon={<ExpandMoreIcon />}
      sx={{
        backgroundColor: "#e8f5e9",
        borderTop: "1px solid #c8e6c9",
        borderBottom: "1px solid #c8e6c9",
        minHeight: 56,
        "&.Mui-expanded": {
          minHeight: 56,
        },
      }}
    >
      <Typography
        sx={{
          fontWeight: "bold",
          color: "#000",
        }}
      >
        {title}
      </Typography>
    </AccordionSummary>
    <AccordionDetails sx={{ p: 0 }}>{children}</AccordionDetails>
  </Accordion>
);

// Tournament Header Component
const TournamentHeader = ({
  title,
  subtitle,
  status,
  presentedBy,
  approvals,
}) => (
  <Box
    sx={{
      p: { xs: 2, sm: 4 },
      pb: { xs: 2, sm: 3 },
      background:
        "linear-gradient(135deg, rgb(235, 235, 235) 0%, rgb(228, 228, 228) 100%)",
      borderRadius: { xs: 0, sm: 2 },
      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      position: "relative",
      overflow: "hidden",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        height: "4px",
        background: "linear-gradient(90deg,rgb(189, 189, 189),rgb(201, 201, 201),rgb(216, 216, 216))",
      },
    }}
  >
    {/* Presented By */}
    <Typography
      variant="overline"
      align="center"
      sx={{
        display: "block",
        fontSize: {
          xs: "1.5rem",
          sm: "2rem",
          md: "2.5rem",
        },
        fontWeight: 700,
        lineHeight: 1.2,
        letterSpacing: 2,
        color: "rgb(82, 82, 82)",
        textTransform: "uppercase",
      }}
    >
      {presentedBy}
    </Typography>
    {presentedBy && (
      <Typography
        variant="h2"
        component="h2"
        align="center"
        sx={{
          fontSize: { xs: "0.8rem", sm: "1rem", md: "1.25rem" },

          color: "#37474f",
          mb: 2,

          background: "linear-gradient(135deg,rgb(112, 112, 112),rgb(117, 117, 117))",
          backgroundClip: "text",
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          textShadow: "0 1px 2px rgba(0,0,0,0.1)",
          position: "relative",
          "&::after": {
            content: '""',
            position: "absolute",
            bottom: -4,
            left: "50%",
            transform: "translateX(-50%)",
            width: "60px",
            height: "2px",
            background:
              "linear-gradient(90deg, transparent,rgb(87, 87, 87), transparent)",
          },
        }}
      >
        Presents
      </Typography>
    )}

    {/* Main Title */}
    <Typography
      variant="h1"
      component="h1"
      align="center"
      sx={{
        fontWeight: 700,
        fontSize: {
          xs: "1.5rem",
          sm: "2rem",
          md: "2.5rem",
        },
        lineHeight: 1.1,
        mb: 2,
        background: "linear-gradient(135deg, rgb(83, 83, 83),rgb(97, 97, 97))",
        backgroundClip: "text",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        textShadow: "0 2px 4px rgba(0,0,0,0.1)",
      }}
    >
      {title}
    </Typography>

    {/* Subtitle */}
    <Typography
      variant="h4"
      align="center"
      sx={{
        fontSize: { xs: "1rem", sm: "1.25rem", md: "1.5rem" },
        fontWeight: 500,
        color: "rgb(82, 82, 82)",
        mb: 3,
        maxWidth: "700px",
        mx: "auto",
        lineHeight: 1.3,
        textShadow: "0 1px 3px rgba(0,0,0,0.1)",
        background: "linear-gradient(135deg,rgb(114, 114, 114),rgb(107, 107, 107))",
        backgroundClip: "text",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        position: "relative",
        px: 2,
        // "&::before": {
        //   content: '"❮"',
        //   position: "absolute",
        //   left: 0,
        //   color: "#42a5f5",
        //   fontSize: "1.2em",
        //   opacity: 0.6,
        // },
        // "&::after": {
        //   content: '"❯"',
        //   position: "absolute",
        //   right: 0,
        //   color: "#42a5f5",
        //   fontSize: "1.2em",
        //   opacity: 0.6,
        // },
      }}
    >
      {subtitle}
    </Typography>

    {/* Approvals */}
    {approvals && (
      <Typography
        variant="body2"
        align="center"
        sx={{
          fontSize: { xs: "0.875rem", sm: "1rem" },
          color: "rgb(82, 82, 82)",
          mb: 3,
          fontStyle: "italic",
        }}
      >
        {approvals}
      </Typography>
    )}

    {/* Status Badge */}
    <Box
      sx={{
        display: "flex",
        justifyContent: "flex-end",
        alignItems: "center",
        width: "100%",
        mt: 2,
      }}
    >
      <Box
        sx={{
          display: "inline-flex",
          alignItems: "center",
          gap: 1,
          px: 3,
          py: 1,
          borderRadius: 20,
          backgroundColor: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.3)",
          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: "50%",
            backgroundColor: status.color,
            boxShadow: `0 0 8px ${status.color}50`,
          }}
        /> */}
        <Typography
          variant="body2"
          sx={{
            fontWeight: 600,
            fontSize: { xs: "0.875rem", sm: "1rem" },
            color: "text.primary",
          }}
        >
          Status:
        </Typography>
        <Typography
          variant="body2"
          sx={{
            fontWeight: 700,
            fontSize: { xs: "0.875rem", sm: "1rem" },
            color: status.color,
          }}
        >
          {status.name}
        </Typography>
      </Box>
    </Box>
  </Box>
);

// Helper function to format the rounds display
function formatRoundsDisplay(rounds) {
  if (typeof rounds === "object") {
    const categoryGroups = [];

    // Handle nested structure: {maleAgeCategory: {U30: 1}, femaleAgeCategory: {U30: 1}}
    Object.entries(rounds).forEach(([categoryKey, categoryData]) => {
      if (typeof categoryData === "object" && categoryData !== null) {
        const ageGroups = [];

        // Extract rounds from nested category data
        Object.entries(categoryData).forEach(([ageGroup, count]) => {
          if (count > 0) {
            const roundText = count === 1 ? "Round" : "Rounds";
            ageGroups.push(`${ageGroup}: ${count} ${roundText}`);
          }
        });

        if (ageGroups.length > 0) {
          const categoryName = categoryKey
            .replace(/([A-Z])/g, " $1")
            .replace(/^./, (str) => str.toUpperCase())
            .replace("Age Category", "")
            .trim();
          categoryGroups.push(`${categoryName} : ( ${ageGroups.join(", ")} )`);
        }
      } else if (typeof categoryData === "number" && categoryData > 0) {
        // Handle direct number values
        const categoryName = categoryKey
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())
          .replace("Age Category", "")
          .trim();
        const roundText = categoryData === 1 ? "Round" : "Rounds";
        categoryGroups.push(`${categoryName}: ${categoryData} ${roundText}`);
      }
    });

    return categoryGroups.length > 0
      ? categoryGroups.join(", ")
      : "The number of rounds will be announced at the venue.";
  }
  return rounds.toString();
}

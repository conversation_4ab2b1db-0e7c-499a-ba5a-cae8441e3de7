import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";

import MenuIcon from "@mui/icons-material/Menu";
import Container from "@mui/material/Container";

import Button from "@mui/material/Button";

import MenuItem from "@mui/material/MenuItem";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Drawer, useMediaQuery } from "@mui/material";

import { Client } from "../../api/client";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import UseToast from "../../lib/hooks/UseToast";
import SignUp from "./Signup";
import Login from "./Login";
import ForgotPassword from "./Forgotpassword";
import { useTheme } from "@emotion/react";

function HeaderSection() {
  const [navOpen, setNavOpen] = React.useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isDesktop = useMediaQuery(theme.breakpoints.up("lg"));

  const { isLoggedIn, clearAuthState, openModel, setOpenModel } =
    UseGlobalContext();
  const toast = UseToast();
  const navigate = useNavigate();
  const location = useLocation();

  // Initialize anchorElUser as null to ensure menu doesn't show on initial load
  const navItems = [
    {
      label: isLoggedIn ? "Dashboard" : "Home",
      path: isLoggedIn ? "/dashboard" : "/",
    },
    // { label: "Players", path: "/players" },
    // { label: "Clubs", path: "/clubs" },
    // { label: "Arbiters", path: "/arbiters" },
    { label: "Tournaments", path: "/tournaments" },
    // { label: "Coaches", path: "/coaches" },
    // { label: "Associations", path: "/associations" },
  ];

  // Don't set active path if it's /dashboard
  const activePath = React.useMemo(() => {
    const path = location.pathname;

    // Check if path starts with any of the nav items paths
    for (const item of navItems) {
      if (
        path === item.path ||
        (path.startsWith(item.path) && item.path !== "/")
      ) {
        return item.path;
      }
    }
    // Default to home if no match
    return "/";
  }, [location.pathname]); // Re-compute whenever the path changes

  const handleOpenNavMenu = React.useCallback((trigger) => {
    setNavOpen(trigger);
  }, []);

  const handleCloseNavMenu = React.useCallback(() => {
    setNavOpen(false);
  }, []);

  const handleLogout = async () => {
    try {
      // Call logout endpoint first
      await Client.get("/auth/logout");

      toast.success("Successfully Logged Out");
      clearAuthState();

      // Navigate after a brief delay to allow cleanup
      setTimeout(() => {
        navigate("/");
      }, 100);
    } catch (error) {
      console.error("Logout error:", error);
      // Force logout even if API call fails
      clearAuthState();
      window.dispatchEvent(new CustomEvent("logout"));
      navigate("/");
    }
  };
  React.useEffect(() => {
    if (isDesktop) {
      setNavOpen(false);
    }
  }, [isDesktop, isLoggedIn]);

  const renderNavItems = React.useMemo(
    () =>
      navItems.map((page, index) => (
        <MenuItem
          key={index}
          onClick={handleCloseNavMenu}
          sx={{
            width: "100%",
            ":hover": { backgroundColor: "#212121" },
            textDecoration: "none",

            textAlign: "start",
            color: activePath === page.path ? "yellow" : "white",
          }}
          component={Link}
          to={page.path}
        >
          <Typography
            sx={{ textAlign: "start", width: "100%", fontSize: "16px" }}
            variant="h6"
          >
            {page.label}
          </Typography>
        </MenuItem>
      )),
    [handleCloseNavMenu, activePath]
  );

  const renderDesktopNavItems = React.useMemo(
    () =>
      navItems.map((page) => (
        <Link
          to={page.path}
          key={page.label}
          style={{ textDecoration: "none", color: "white", mx: "auto" }}
        >
          <Button
            onClick={handleCloseNavMenu}
            sx={{
              color: activePath === page.path ? "yellow" : "white",
              display: "block",
              fontSize: "16px",
              p: "0px !important",
              ":hover": { color: "yellow", cursor: "hover" },
            }}
          >
            {page.label}
          </Button>
        </Link>
      )),
    [handleCloseNavMenu, activePath]
  );

  return (
    <AppBar
      position="static"
      sx={{
        padding: 0,
      }}
    >
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          <Link
            to={isLoggedIn ? "/dashboard" : "/"}
            style={{ textDecoration: "none" }}
          >
            <Typography
              variant="h6"
              noWrap
              sx={{
                mr: 2,
                display: { xs: "none", lg: "flex" },
                fontFamily: "'Prosto One', cursive",
                fontWeight: 400,
                letterSpacing: ".3rem",
                color: "inherit",
                textDecoration: "none",
                fontSize: "1.5rem",
              }}
            >
              ChessBrigade.com
            </Typography>
          </Link>

          <Box sx={{ flexGrow: 1, display: { xs: "flex", lg: "none" } }}>
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={() => handleOpenNavMenu(true)}
              color="inherit"
            >
              <MenuIcon />
            </IconButton>
            <Drawer
              open={navOpen}
              onClose={() => handleOpenNavMenu(false)}
              anchor="left"
              disableScrollLock={true}
              slotProps={{
                backdrop: {
                  sx: {
                    width: "100%",
                  },
                },
              }}
            >
              <Box
                sx={{
                  width: "250px",
                  backgroundColor: "black",
                  height: "100%",
                  pt: "20px",
                }}
              >
                {renderNavItems}
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    justifyContent: "center",
                    alignItems: "center",
                    mt: 2,
                    mx: 2,
                  }}
                >
                  {isMobile &&
                    (isLoggedIn === true ? (
                      <Box
                        sx={{
                          display: "flex",
                          gap: 2,
                          justifyContent: "center",
                          alignItems: "flex-end",
                          width: "100%",
                        }}
                      >
                        <Button
                          variant="contained"
                          size="small"
                          sx={{
                            borderRadius: 1,
                            bgcolor: "white",
                            width: "100%",

                            color: "#000",

                            "&:hover": {
                              bgcolor: "rgba(255, 255, 255, 0.8) ",
                            },
                            textWrap: "nowrap",
                            px: { xs: 2, lg: 4 },
                          }}
                          onClick={handleLogout}
                        >
                          Log Out
                        </Button>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: "flex",
                          gap: 2,
                          width: "100%",
                          flexDirection: "column",

                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{
                            borderRadius: 1,
                            borderColor: "rgba(255, 255, 255, 0.5)",
                            bgcolor: "rgba(255, 255, 255, 0.1)",
                            width: "100%",

                            color: "#fff",
                            ":hover": {
                              bgcolor: "rgba(255, 255, 255, 0.38)",
                              borderColor: "rgba(255, 255, 255, 0.5)",
                            },

                            textWrap: "nowrap",
                            px: { xs: 2, lg: 4 },
                          }}
                          onClick={() =>
                            setOpenModel({ ...openModel, signup: true })
                          }
                        >
                          Sign Up
                        </Button>

                        <Button
                          variant="contained"
                          size="small"
                          sx={{
                            borderRadius: 1,
                            bgcolor: "white",
                            width: "100%",

                            color: "#000",

                            "&:hover": {
                              bgcolor: "rgba(255, 255, 255, 0.8) ",
                            },
                            textWrap: "nowrap",
                            px: { xs: 2, lg: 4 },
                          }}
                          onClick={() =>
                            setOpenModel({ ...openModel, login: true })
                          }
                        >
                          Log In
                        </Button>
                      </Box>
                    ))}
                </Box>
              </Box>
            </Drawer>
          </Box>
          {!isLoggedIn && (
            <>
              <Login open={openModel.login} setOpen={setOpenModel} />
              <SignUp open={openModel.signup} setOpen={setOpenModel} />
              <ForgotPassword
                open={openModel.forgotpassword}
                setOpen={setOpenModel}
              />
            </>
          )}

          <Typography
            variant="h5"
            noWrap
            component={Link}
            to={isLoggedIn ? "/dashboard" : "/"}
            sx={{
              mr: 2,
              display: { xs: "flex", lg: "none" },
              flexGrow: 1,
              width: "fit-content",
              fontFamily: "'Prosto One', cursive",
              fontWeight: 400,
              letterSpacing: ".3rem",
              color: "inherit",
              textDecoration: "none",
              fontSize: "1.2rem",
              ":hover": { bgcolor: "none" },
            }}
          >
            ChessBrigade.com
          </Typography>
          <Box
            sx={{
              flexGrow: 1,
              display: {
                xs: "none",
                lg: "flex",
              },
              justifyContent: "flex-start",
              alignItems: "flex-end",
            }}
          >
            <Box sx={{ display: "flex", gap: "30px",mt:0.6 ,alignItems:'flex-end' ,justifyContent:'center'}}>
              {renderDesktopNavItems}
            </Box>
          </Box>
          {isLoggedIn === true
            ? !isMobile && (
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      borderRadius: 1,
                      bgcolor: "white",
                      color: "#000",

                      "&:hover": {
                        bgcolor: "rgba(255, 255, 255, 0.8) ",
                      },
                      textWrap: "nowrap",
                      px: { xs: 2, lg: 4 },
                    }}
                    onClick={handleLogout}
                  >
                    Log Out
                  </Button>
                </Box>
              )
            : !isMobile && (
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{
                      borderRadius: 1,
                      borderColor: "rgba(255, 255, 255, 0.5)",
                      bgcolor: "rgba(255, 255, 255, 0.1)",
                      color: "#fff",
                      ":hover": {
                        bgcolor: "rgba(255, 255, 255, 0.38)",
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },

                      textWrap: "nowrap",
                      px: { xs: 2, lg: 4 },
                    }}
                    onClick={() => setOpenModel({ ...openModel, signup: true })}
                  >
                    Sign Up
                  </Button>

                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      borderRadius: 1,
                      bgcolor: "white",
                      color: "#000",

                      "&:hover": {
                        bgcolor: "rgba(255, 255, 255, 0.8) ",
                      },
                      textWrap: "nowrap",
                      px: { xs: 2, lg: 4 },
                    }}
                    onClick={() => setOpenModel({ ...openModel, login: true })}
                  >
                    Log In
                  </Button>
                </Box>
              )}
        </Toolbar>
      </Container>
    </AppBar>
  );
}

export default React.memo(HeaderSection);

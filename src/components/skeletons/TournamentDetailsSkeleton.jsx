import React from "react";
import {
  <PERSON>,
  Button,
  Container,
  Paper,
  Skeleton,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

/**
 * Skeleton component for the TournamentDetailsPage
 * Shows loading placeholders while tournament data is being fetched
 */
const TournamentDetailsSkeleton = () => {
  // Define sections to match the actual tournament details view
  const sections = [
    { id: "tournamentDetails", title: "Tournament Details:" },
    { id: "personalInfo", title: "Personnel & Contact Info:" },
    { id: "venueDetails", title: "Tournament Venue Details:" },
    { id: "prizeDetails", title: "Tournament Prize Details:" },
    { id: "miscDetails", title: "Miscellaneous Details:" },
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: "hidden",
          mb: 4,
          backgroundColor: "#f5f9f6",
        }}
      >
   
        <Box
          sx={{
            p: { xs: 2, sm: 4 },
            pb: { xs: 2, sm: 3 },
             background:
        "linear-gradient(135deg, rgb(235, 235, 235) 0%, rgb(228, 228, 228) 100%)",
            borderRadius: { xs: 0, sm: 2 },
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
            position: "relative",
            overflow: "hidden",
            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              height: "4px",
             background: "linear-gradient(90deg,rgb(189, 189, 189),rgb(201, 201, 201),rgb(216, 216, 216))",
            },
          }}
        >
          {/* Skeleton for Presented By */}
          <Skeleton
            variant="text"
            width="40%"
            height={24}
            sx={{
              mx: "auto",
              mb: 2,
            }}
          />

          {/* Skeleton for Title */}
          <Skeleton
            variant="text"
            width="70%"
            height={40}
            sx={{
              mx: "auto",
              mb: 2,
            }}
          />

          {/* Skeleton for Subtitle */}
          <Skeleton
            variant="text"
            width="60%"
            height={28}
            sx={{
              mx: "auto",
              mb: 3,
            }}
          />

          {/* Skeleton for Approvals (if applicable) */}
          <Skeleton
            variant="text"
            width="40%"
            height={20}
            sx={{
              mx: "auto",
              mb: 3,
            }}
          />

          {/* Skeleton for Status Badge */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
            }}
          >
            <Skeleton
              variant="rounded"
              width={160}
              height={36}
              sx={{
                borderRadius: 20,
              }}
            />
          </Box>
        </Box>
        {/* Section Accordions */}
        {sections.map((section) => (
          <Accordion
            key={section.id}
            defaultExpanded={true}
            sx={{
              boxShadow: "none",
              "&:before": {
                display: "none",
                margin: "0 !important",
              },
              "&.MuiAccordion-root.Mui-expanded": {
                margin: "0 !important",
              },
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                backgroundColor: "#e8f5e9",
                borderTop: "1px solid #c8e6c9",
                borderBottom: "1px solid #c8e6c9",
                minHeight: 56,
                "&.Mui-expanded": {
                  minHeight: 56,
                },
              }}
            >
              <Typography
                sx={{
                  fontWeight: "medium",
                  color: "#2e7d32",
                }}
              >
                {section.title}
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 0 }}>
              {/* Detail rows skeleton */}
              <Box sx={{ p: 2 }}>
                {Array(5)
                  .fill(0)
                  .map((_, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: "flex",
                        borderBottom: "1px solid #e0e0e0",
                        py: 1.5,
                      }}
                    >
                      <Skeleton
                        variant="rectangular"
                        width="40%"
                        height={24}
                        sx={{ borderRadius: 1 }}
                      />
                      <Box sx={{ flexGrow: 1 }} />
                      <Skeleton
                        variant="rectangular"
                        width="50%"
                        height={24}
                        sx={{ borderRadius: 1 }}
                      />
                    </Box>
                  ))}
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}
      </Paper>

      {/* Action Buttons Skeleton */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          flexWrap: "wrap",
          justifyContent: "center",
          gap: 2,
          p: 2,
          borderBottom: "1px solid #f0f0f0",
        }}
      >
        <Skeleton
          variant="rectangular"
          width={120}
          height={36}
          sx={{ borderRadius: 1 }}
        />
        <Skeleton
          variant="rectangular"
          width={160}
          height={36}
          sx={{ borderRadius: 1 }}
        />
        <Skeleton
          variant="rectangular"
          width={140}
          height={36}
          sx={{ borderRadius: 1 }}
        />
      </Box>
    </Container>
  );
};

export default TournamentDetailsSkeleton;

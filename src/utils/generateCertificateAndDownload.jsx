import { pdf } from "@react-pdf/renderer";
import { saveAs } from "file-saver";

export const generateCertificateAndDownload = async ({
  toast,
  certificateData,
}) => {

  try {
    // Await the dynamic import and get the default export
    const { default: CertificateTemplate } = await import(
      "../components/certificates/CertificateTemplate.jsx"
    );
    const rawdata = buildCertificateData(certificateData);
    const pdfBlob = await pdf(<CertificateTemplate data={rawdata} />).toBlob();

    const fileName = `certificate_${
      certificateData.playerName?.replace(/\s+/g, "_") || "player"
    }.pdf`;

    saveAs(pdfBlob, fileName);

    if (toast) {
      toast.success("Certificate downloaded successfully!");
    }
  } catch (error) {
    console.error("Certificate generation failed:", error);
    if (toast) {
      toast.error("Failed to generate certificate PDF");
    }
  }
};

export function formatCategory(ageCategory, genderCategory) {
  const genderMap = {
    male: "Boys",
    female: "Girls",
  };

  if (!ageCategory || ageCategory.toUpperCase() === "OPEN") {
    return "OPEN";
  }

  const age = ageCategory.toUpperCase(); // e.g., "U08"
  const gender = genderMap[genderCategory?.toLowerCase()] || "Players";

  return `${age} ${gender}`;
}

export function formatRank(rank, topNumber = 3) {
  if (rank == null) return "";

  // For top N players (based on topNumber), show their actual rank with place
  if (rank <= topNumber) {
    const suffixes = ["th", "st", "nd", "rd"];
    const v = rank % 100;
    const suffix = suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0];
    return `${rank}${suffix} Place`;
  }

  // For all other players (rank > topNumber), show participation
  return "Participation";
}

export function buildCertificateData(data) {
  return {
    organization: data.organization || "Sample Organization",
    tournamentTitle: data.tournamentTitle || "Sample Tournament",
    subtitle: data.subtitle || data.metadata?.subtitle || "Sample Subtitle",
    poweredBy: data.poweredBy || data.metadata?.poweredBy || "ChessBrigade.com",
    playerName: data.playerName || "Player Name",
    tournamentDate:
      data.tournamentDate || data.metadata?.tournamentDate || "Unknown Date",
    venue: data.venue || data.metadata?.venue || "Sample Venue",
    points: String(data.totalPoints ?? "0").padStart(2, "0"),
    totalRounds: "07", // Set dynamically if you have it elsewhere
    category: formatCategory(data.ageCategory, data.genderCategory),
    position: formatRank(data.rank, data.topNumber),
    rank: data.rank,
    timestamp: new Date().toLocaleDateString(),
    chiefArbiterSignature: data.metadata.chiefArbiterSignature,
    tournamentDirectorSignature: data.metadata.tournamentDirectorSignature,
  };
}


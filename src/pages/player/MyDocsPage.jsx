import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Grid,
  CircularProgress,
  Paper,
  Divider,
  Container,
  But<PERSON>,
} from "@mui/material";

import DocumentUpload from "../../components/documents/DocumentUpload";
import DocumentList from "../../components/documents/DocumentList";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import BackButton from "../../components/common/BackButton";

const MyDocsPage = () => {

  const [open, setOpen] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const toast = UseToast();

  // Fetch documents on component mount
  useEffect(() => {
    fetchDocuments();
  }, []);
  const onClose = () => {
    setOpen(false);
  };

  // Function to fetch documents from the API
  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/player/documents");
      if (response.data.success) {
        setDocuments(response.data.data || []);
      } else {
        toast.error(response.data.message || "Failed to fetch documents");
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
    } finally {
      setLoading(false);
    }
  };

  // Function to handle document upload
  const handleUpload = async (file, fileName) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append("document", file);
      formData.append("name", fileName);

      const response = await Client.post("/player/documents/", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        toast.success("Document uploaded successfully");
        fetchDocuments(); // Refresh the document list
      } else {
        toast.error(response.data.message || "Failed to upload document");
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again later.");
    } finally {
      setUploading(false);
    }
  };

  // Function to handle document deletion
  const handleDelete = async (id) => {
    try {
      const response = await Client.delete(`/player/documents/${id}`);
      if (response.data.success) {
        toast.success("Document deleted successfully");
        // Update the documents list by removing the deleted document
        setDocuments(documents.filter((doc) => doc.id !== id));
      } else {
        toast.error(response.data.message || "Failed to delete document");
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error("Failed to delete document. Please try again later.");
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      <Box sx={{ display: "flex", justifyContent: "space-between", pb: 2 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom sx={{fontSize:{xs:"1rem",md:"2rem"}}}>
          My Documents
        </Typography>
        <Button
          variant="contained"
          disabled={documents.length >= 8}
          sx={{ bgcolor: "hsla(242, 56%, 36%, 1) ", fontSize: { xs: "0.8rem", md: "1rem" } }}
          onClick={() => setOpen(true)}
        >
          Upload Document
        </Button>
      </Box>

      <DocumentUpload
        open={open}
        onClose={onClose}
        onUpload={handleUpload}
        isUploading={uploading}
      />

      {/* Documents List Section */}
      <Grid item xs={12} md={7}>
        <Paper elevation={3} sx={{ p: 3, borderRadius: 2, minHeight: "400px" }}>
          {loading ? (
            <Box
              display="flex"
              justifyContent="center"
              alignItems="center"
              height="300px"
            >
              <CircularProgress />
            </Box>
          ) : (
            <DocumentList documents={documents} onDelete={handleDelete} />
          )}
        </Paper>
      </Grid>
    </Container>
  );
};

export default MyDocsPage;

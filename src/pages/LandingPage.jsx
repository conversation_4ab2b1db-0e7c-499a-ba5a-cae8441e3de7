import Player from "@vimeo/player";

import {
  <PERSON>,
  <PERSON><PERSON>,
  Chip,
  Container,
  Grid,
  LinearProgress,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";

import React, { useEffect, useRef } from "react";
import { keyframes } from "@emotion/react";

// import HeroBackgroundImage from "../assets/images/homepageBackgroundchess.png";
import HeroBackgroundImage from "../assets/images/crop.png";
import CorporateStrategy from "../assets/images/corporate-strategy.png";
import Calendar from "../assets/images/calendar.png";
import ComputerChess from "../assets/images/computerchess.png";
import { Link, useNavigate } from "react-router-dom";

import useGlobalContext from "../lib/hooks/UseGlobalContext";

const features = [
  {
    title: "Puzzles",
    icon: CorporateStrategy,
    description: "Practice with chess puzzles and improve your game",
    link: "#",
  },
  {
    title: "Register for Tournaments",
    icon: Calendar,
    description: "Find and register for chess tournaments near you",
    link: "#",
  },
  {
    title: "Play Online",
    icon: ComputerChess,
    description: "Challenge players from around the world in online matches",
    link: "#",
  },
];

const LandingPage = () => {
  const { isLoggedIn } = useGlobalContext();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const playerRef = useRef(null);

  useEffect(() => {
    if (isLoggedIn) {
      navigate("/dashboard");
    }
  }, []);

  useEffect(() => {
    if (!playerRef.current) return;

    const player = new Player(playerRef.current, {
      id: 1093312681,
      autoplay: true,
      controls: true,
      loop: true,
      byline: false,
      title: false,
      portrait: false,
      muted: false,
      width: isMobile ? 300 : 800,
      height: isMobile ? 200 : 400
    });

    player.on("loaded", () => {
      console.log("Vimeo video loaded");
    });

    return () => {
      player.destroy();
    };
  }, []);

  const fadeInUp = keyframes`
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
  `;

  // Animation for the Register Now button
  const pulseGlow = keyframes`
    0% { 
      box-shadow: 0 0 5px rgba(255, 87, 34, 0.7);
      transform: scale(1);
    }
    50% { 
      box-shadow: 0 0 20px rgba(255, 87, 34, 0.9), 0 0 30px rgba(255, 87, 34, 0.6);
      transform: scale(1.05);
    }
    100% { 
      box-shadow: 0 0 5px rgba(255, 87, 34, 0.7);
      transform: scale(1);
    }
  `;

  const pulseGlow2 = keyframes`
    0% { 
      box-shadow: 0 0 5px rgba(34, 255, 82, 0.7);
      transform: scale(1);
    }
    50% { 
      box-shadow: 0 0 20px rgba(34, 255, 82, 0.7), 0 0 30px rgba(34, 255, 82, 0.7);
      transform: scale(1.05);
    }
    100% { 
      box-shadow: 0 0 5px rgba(34, 255, 82, 0.7);
      transform: scale(1);
    }
  `;

  const shimmer = keyframes`
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  `;

  return (
    <Box
      sx={{
        bgcolor: "background.paper",
        overflowX: "hidden",
        minHeight: {
          xs: "50vh",
          sm: "80vh",
          md: "130vh",
          lg: "110vh",
          xl: "110vh",
        }, // do some change in minheight for option remove
      }}
    >
      <Box
        sx={{
          bgcolor: "white",
          minHeight: {
            xs: "50vh",
            sm: "80vh",
            md: "120vh",
            lg: "110vh",
            xl: "110vh", // do some change in minheight for option remove
          },
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
        className="jagadeesh"
      >
        <Box
          sx={{
            position: "relative",
            textAlign: "center",
            pb: 4,
          }}
        >
          <Box
            sx={{
              position: "relative",
              zIndex: 5,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              direction: "column",
              py: 3,
            }}
          >
            <Box
              component="img"
              src={HeroBackgroundImage}
              alt="Chess pieces"
              sx={{
                position: "absolute",
                width: "100%",
                height: "auto",
                opacity: 0.3,
                top: {
                  xs: 100,
                  sm: 80,
                  md: 80,
                  lg: 60,
                  xl: 80,
                },
                left: 0,
                right: 0,
                transform: "rotate(0.55deg)",
                margin: "0 auto",
                zIndex: 4,
              }}
            />
            <Box
              component="svg"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="xMidYMid meet"
              viewBox="0 0 900 414"
              sx={{
                position: "absolute",
                top: 0,
                margin: "0 auto",
                opacity: 0.4,
                width: {
                  xs: "150vw",
                  sm: "120vw",
                  md: "110vw",
                  lg: "100vw",
                  xl: "100%",
                },
                height: "auto",
                zIndex: -1,
              }}
            >
              <path
                d="M900 0.5V281.771L462.5 413.5L0 293.51V0.5H900Z"
                fill="url(#paint0_linear_220_1129)"
                fillOpacity="0.23"
              />
              <defs>
                <linearGradient
                  xmlns="http://www.w3.org/2000/svg"
                  id="paint0_linear_220_1129"
                  x1="900"
                  y1="177.331"
                  x2="-0.000375146"
                  y2="167.983"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#9F9F9F" />
                  <stop offset="0.333333" stopColor="white" />
                  <stop offset="0.666667" stopColor="white" />
                  <stop offset="1" stopColor="#9F9F9F" />
                </linearGradient>
              </defs>
            </Box>
            <Container
              maxWidth="lg"
              sx={{
                position: "relative",
                zIndex: 5,
                transition: "all 0.3s ease-in-out",
              }}
            >
              <Chip
                label="Upcoming Tournament"
                variant="outlined"
                size="medium"
                sx={{
                  fontWeight: 700,
                  fontSize: {
                    xs: 10,    // Extra small screens
                    sm: 11,    // Small screens
                    md: 12,    // Medium screens and up
                  },
                  fontStyle: "italic",
                  fontFamily: "'Prosto One', cursive",
                  p: {
                    xs: 0.5,   // Less padding on mobile
                    sm: 0.75,  // Medium padding on tablets
                    md: 1,     // Full padding on desktop
                  },
                  borderRadius: "20px",
                  position: "relative",
                  color: "#fff",
                  background: "linear-gradient(45deg, #4CAF50 30%, #CDDC39 90%)",
                  backgroundSize: "200% 100%",
                  border: "none",
                  cursor: "pointer",
                  overflow: "hidden",
                  animation: `${pulseGlow2} 2s ease-in-out infinite`,
                  transition: "all 0.3s ease",
                  minWidth: {
                    xs: "auto",     // Auto width on mobile
                    sm: "120px",    // Minimum width on tablets
                    md: "140px",    // Larger minimum width on desktop
                  },
                  maxWidth: {
                    xs: "90vw",     // Max 90% of viewport width on mobile
                    sm: "200px",    // Fixed max width on larger screens
                  },
                  height: {
                    xs: "28px",     // Smaller height on mobile
                    sm: "32px",     // Medium height on tablets
                    md: "36px",     // Full height on desktop
                  },

                  // Hover effects - disabled on touch devices
                  "@media (hover: hover)": {
                    "&:hover": {
                      background: "linear-gradient(45deg, #388E3C 30%, #C0CA33 90%)",
                      transform: "translateY(-2px)",
                      boxShadow: {
                        xs: "0 3px 10px rgba(205, 220, 57, 0.3)",  // Lighter shadow on mobile
                        md: "0 5px 15px rgba(205, 220, 57, 0.4)",  // Full shadow on desktop
                      },
                    },
                  },

                  // Touch-friendly active state
                  "&:active": {
                    transform: "translateY(0px)",
                    boxShadow: "0 2px 8px rgba(205, 220, 57, 0.2)",
                  },

                  "&:before": {
                    content: '""',
                    position: "absolute",
                    top: 0,
                    left: "-100%",
                    width: "100%",
                    height: "100%",
                    background: "linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)",
                    animation: `${shimmer} 2s ease-in-out infinite`,
                    zIndex: 1,
                  },

                  "& .MuiChip-label": {
                    position: "relative",
                    zIndex: 2,
                    whiteSpace: {
                      xs: "nowrap",   // Prevent wrapping on mobile
                      sm: "normal",   // Allow wrapping on larger screens if needed
                    },
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    lineHeight: {
                      xs: 1.2,       // Tighter line height on mobile
                      md: 1.4,       // Normal line height on desktop
                    },
                  },

                  // Reduce motion for users who prefer it
                  "@media (prefers-reduced-motion: reduce)": {
                    animation: "none",
                    transition: "none",
                    "&:before": {
                      animation: "none",
                    },
                  },

                  // High contrast mode support
                  "@media (prefers-contrast: high)": {
                    border: "2px solid #fff",
                    "&:hover": {
                      border: "2px solid #000",
                    },
                  },
                }}
              />
              <Typography
                variant={isMobile ? "h4" : "h3"}
                component="h1"
                sx={{
                  fontWeight: "regular",
                  fontSize: {
                    xs: "25px",
                    sm: "30px",
                    md: "40px",
                    lg: "42px",
                    xl: "46px",
                  },
                }}
              >
                <Typography
                  sx={{
                    fontWeight: "bold",
                    fontStyle: "italic",
                    fontFamily: "'Prosto One', cursive",
                    fontSize: {
                      xs: "25px",
                      sm: "30px",
                      md: "40px",
                      lg: "42px",
                      xl: "46px",
                    },
                  }}
                >
                  St. Joseph's School of Excellence
                </Typography>
                <Typography
                  sx={{
                    fontWeight: "regular",
                    fontSize: "1rem",
                  }}
                >
                  Presents
                </Typography>
                <Typography
                  sx={{
                    fontWeight: "bold",
                    fontFamily: "'Prosto One', cursive",
                    fontSize: {
                      xs: "25px",
                      sm: "30px",
                      md: "40px",
                      lg: "42px",
                      xl: "46px",
                    },
                  }}
                >
                  Cup of Jai
                </Typography>
                <Typography
                  sx={{
                    fontWeight: "regular",
                    mb: { xs: 1, sm: 2, md: 3 },
                    fontSize: "1.5rem",
                  }}
                >
                  State Level Children's Chess Tournament
                </Typography>
              </Typography>
              <Typography variant='body1' mb={4} sx={{ lineHeight: '12px', fontSize: 18, fontWeight: 500 }}>Date: 13th July 2025.</Typography>
              <Typography
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: { xs: 0.5, sm: 1 },
                  color: "#000",
                  fontWeight: 500,
                  lineHeight: { xs: 1.6, sm: 2 },
                  fontSize: { xs: "0.9rem", sm: "1rem", md: "1.1rem" },
                  animation: `${fadeInUp} 1s ease-out`,
                  flexWrap: "wrap",
                  textAlign: "center",
                  px: { xs: 2, sm: 3 },
                  py: { xs: 1, sm: 1.5 },
                  "& .registration-text": {
                    color: "green",
                    fontWeight: 600,
                    display: "flex",
                    flexDirection: { xs: "column", sm: "row" },
                    alignItems: "center",
                    gap: { xs: 1, sm: 1 },
                    flexWrap: "wrap",
                    justifyContent: "center",
                  },
                  "& .register-link": {
                    ml: { xs: 0, sm: 1 },
                    px: { xs: 1.5, sm: 2 },
                    py: { xs: 0.4, sm: 0.5 },
                    borderRadius: "20px",
                    background:
                      "linear-gradient(45deg, #FF5722 30%, #FF7043 90%)",
                    backgroundSize: "200% 100%",
                    color: "#fff",
                    fontWeight: 600,
                    fontSize: { xs: "0.85rem", sm: "0.9rem", md: "1rem" },
                    position: "relative",
                    overflow: "hidden",
                    animation: `${pulseGlow} 2s ease-in-out infinite`,
                    transition: "all 0.3s ease",
                    textDecoration: "none",
                    cursor: "pointer",
                    display: "inline-block",
                    minWidth: "fit-content",
                    whiteSpace: "nowrap",
                    "&:hover": {
                      background: "linear-gradient(45deg, #E64A19 30%, #FF5722 90%)",
                      transform: "translateY(-1px)",
                      boxShadow: {
                        xs: "0 3px 10px rgba(255, 87, 34, 0.3)",
                        sm: "0 5px 15px rgba(255, 87, 34, 0.4)"
                      },
                    },
                    "&:before": {
                      content: '""',
                      position: "absolute",
                      top: 0,
                      left: "-100%",
                      width: "100%",
                      height: "100%",
                      background:
                        "linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)",
                      animation: `${shimmer} 2s ease-in-out infinite`,
                    },
                  },
                  "& .deadline-text": {
                    fontSize: { xs: "0.8rem", sm: "0.9rem", md: "1rem" },
                    mt: { xs: 0.5, sm: 0 },
                  }
                }}
              >
                <span className="registration-text">

                  <span>Registration Open</span>
                  <a
                    href="/tournaments/cup-of-jai"
                    className="register-link"
                  >
                    Register Now
                  </a>
                  <span className="deadline-text">
                    Registration closes on 11th July 2025 6:00 PM.
                  </span>
                </span>

              </Typography>
              <Box
                component="div"
                sx={{
                  mt: 4,
                  width: '100%',
                  height: "400px",
                  borderRadius: "30px",
                }}
              >
                <div
                  ref={playerRef}
                  style={{

                    width: "100%",
                    height: "400px"
                  }}
                />
              </Box>
            </Container>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default LandingPage;

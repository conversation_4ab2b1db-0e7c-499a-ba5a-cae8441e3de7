import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Container, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import { Link, useNavigate, useParams } from "react-router-dom";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import PaymentModal from "../../components/payment/PaymentModal";
import useGlobalContext from "../../lib/hooks/UseGlobalContext";
import TournamentDetailsView from "../../components/common/TournamentDetailsView";
import TournamentDetailsSkeleton from "../../components/skeletons/TournamentDetailsSkeleton";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import BackButton from "../../components/common/BackButton";

const AdminTournamentDetailsPage = () => {
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);

  const [tournament, setTournament] = useState({});
  const [loading, setLoading] = useState(true);
  const [players, setPlayers] = useState([])


  const { user } = useGlobalContext();
  const toast = UseToast();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDetails = async () => {
      if (!title) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${newTitle}`);
        if (response.data.success) {
          setTournament(response.data.data);

         
        } else {
          toast.info(
            response.data.message || "Failed to load tournament details"
          );
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error(
          "Error fetching tournament details. Please try again later."
        );
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
  }, [newTitle, user]);




    const fetchPlayers = async (type) => {
      setLoading(true);
      try {
        const response = await Client.get(`admin/details/${newTitle}/players`, {
        });
        if (!response.data.success) {
          toast.info(response.data.error.massage);
          return;
        }
        if (response.data.status === 204) {
          toast.info("No Player found");
          setPlayers([]);
          return;
        }
       
        setPlayers(response.data.data.players);
     
        navigate(`/dashboard/${type}/compose`, {
          state: {
            selectedUsers:response.data.data.players,
            selectedType: "player",
            mode: type,
          },
        });
      } catch (error) {
        console.error("Error searching players:", error);
        toast.error("An error occurred while searching for players");
      } finally {
        setLoading(false);
      }
    };

  return (
    <>
      {loading ? (
        <TournamentDetailsSkeleton />
      ) : (
        <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
          {/* Payment Success Alert */}
          <BackButton />

          <div style={{ minHeight: "100vh" }}>
            {tournament && <TournamentDetailsView tournaments={tournament} />}
          </div>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
              justifyContent: "center",
              gap: 2,
              p: 2,
              borderBottom: "1px solid #f0f0f0",
            }}
          >

            <Button
              variant="contained"
              color="success"
              onClick={()=>fetchPlayers('email')}
              sx={{
                borderRadius: 1,
                textTransform: "none",
                fontWeight: 500,
                fontSize: 16,
                px: 2,
                bgcolor: "#166DA3", // Default background
                "&:hover": {
                  bgcolor: "#1A7FBF", // Lighter blue on hover
                  boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(-2px)",
                  transition: "all 0.3s ease",
                },
                "&:active": {
                  bgcolor: "#125B87", // Slightly darker blue for active click
                  boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(0)", // Reset push on click
                },
              }}
            >
              Mail
            </Button>

            
            <Button
              variant="contained"
              color="success"
              onClick={()=>fetchPlayers('sms')}
              sx={{
                borderRadius: 1,
                textTransform: "none",
                fontWeight: 500,
                fontSize: 16,
                px: 2,
                bgcolor: "#166DA3", // Default background
                "&:hover": {
                  bgcolor: "#1A7FBF", // Lighter blue on hover
                  boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(-2px)",
                  transition: "all 0.3s ease",
                },
                "&:active": {
                  bgcolor: "#125B87", // Slightly darker blue for active click
                  boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(0)", // Reset push on click
                },
              }}
            >
              Sms
            </Button>

              <Button
              variant="contained"
              color="success"
              onClick={()=>navigate(`/dashboard/tournaments/edit/${newTitle}`)}
              sx={{
                borderRadius: 1,
                textTransform: "none",
                fontWeight: 500,
                fontSize: 16,
                px: 2,
                bgcolor: "#166DA3", // Default background
                "&:hover": {
                  bgcolor: "#1A7FBF", // Lighter blue on hover
                  boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(-2px)",
                  transition: "all 0.3s ease",
                },
                "&:active": {
                  bgcolor: "#125B87", // Slightly darker blue for active click
                  boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(0)", // Reset push on click
                },
              }}
            >
              Edit Tournament
            </Button>

          </Box>
        </Container>
      )}
    </>
  );
};

export default AdminTournamentDetailsPage;
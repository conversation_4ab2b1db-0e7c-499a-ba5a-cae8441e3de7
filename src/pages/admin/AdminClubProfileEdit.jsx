import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>rid,
  Box,
  Avatar,
  Fade,
  Container,
  Paper,
} from "@mui/material";
import { isValid, z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Add, Edit, Person } from "@mui/icons-material";
import FormTextField from "../../components/form/FormTextField";
import FormPhoneInput from "../../components/form/FormPhoneInput";
import FormAutocomplete from "../../components/form/FormAutocomplete";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import { useLocation, useNavigate } from "react-router-dom";
// import MapComponent from "../MapComponent";
import AutoNavigateMapComponent from "../../components/AutoNavigateMapComponent";
import Spinner from "../../components/common/Spinner";
import ProfileImageUpload from "../../components/common/ProfileImageUpload";
import FormNumberField from "../../components/form/FormNumberField";

import BackButton from "../../components/common/BackButton";

// Zod schema for form validation
const clubProfileSchema = z.object({
  clubName: z
    .string()
    .min(2, "Club Name must be at least 2 characters")
    .max(150, "Club Name must not exceed 100 characters"),
  clubDistrictId: z.string().min(1, "Club ID is required"),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  district: z.string().min(1, "District is required"),
  city: z.string().min(1, "City is required"),
  pincode: z.string().regex(/^\d{6}$/, "Pincode must be 6 digits"),
  address: z.string().min(10, "Address must be at least 10 characters"),
  locationUrl: z.string().url("Location URL is Required"),
  authorizedSignatoryName: z
    .string()
    .min(2, "Authorized Signatory Name must be at least 2 characters"),
  authorizedSignatoryContactNumber: z
    .string()
    .regex(/^(91)?\d{10}$/, "Mobile number must be 10 digits"),
  authorizedSignatoryEmail: z.string().email("Email is Required"),
  authorizedSignatoryDesignation: z
    .string()
    .min(2, "Authorized Signatory Designation must be at least 2 characters"),
  profileImage: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "File must be JPEG, PNG, or WebP format" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 3 * 1024 * 1024; // 5MB limit
      },
      { message: "File must be less than 5MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return ["image/jpeg", "image/png", "image/jpg", "image/webp"].includes(
          file.type
        );
      },
      { message: "File must be JPEG, PNG, or WebP format" }
    ),
  // This field is for preview purposes only, not sent to API
  profileImagePreview: z.string().optional(),
  contactPersonName: z
    .string()
    .min(2, "Contact Person Name must be at least 2 characters"),
  contactPersonNumber: z
    .string()
    .regex(/^(91)?\d{10}$/, "Mobile number must be 10 digits"),
  alternateContactNumber: z
    .string()
    .regex(/^(91)?\d{10}$/, "Mobile number must be 10 digits"),
  contactPersonEmail: z.string().email("Email is Required"),
});
const clubProfileEditSchema = clubProfileSchema.optional();

const AdminClubProfileEdit = () => {
  const location = useLocation();
  const { state } = location;
  const { clubId: profileClubId } = state;

  // Initialize React Hook Form
  const { control, handleSubmit, setValue, watch, reset, getValues } = useForm({
    resolver: zodResolver(clubProfileEditSchema),
    defaultValues: {
      clubName: "",
      clubDistrictId: "",
      country: "",
      state: "",
      district: "",
      city: "",
      pincode: "",
      address: "",
      authorizedSignatoryName: "",
      authorizedSignatoryContactNumber: "",
      authorizedSignatoryEmail: "",
      authorizedSignatoryDesignation: "",
      contactPersonName: "",
      contactPersonNumber: "",
      alternateContactNumber: "",
      contactPersonEmail: "",
      locationUrl: "",
      countryCode: "",
      stateCode: "",
    },
    mode: "onChange",
  });
  // Watch for form values and errors

  // Watch for changes to country and state codes for dependent dropdowns
  const countryCode = watch("countryCode");
  const stateCode = watch("stateCode");

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [cities, setCities] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [originalProfileData, setOriginalProfileData] = useState({});
  const toast = UseToast();
  const navigate = useNavigate();
  // const { geoInfo } = useUserGeoInfo();

  // Create a debounced version of setValue

  // Load countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      const response = await Client.get("/location/countries");
      if (!response.data.success) return;
      setCountries(response.data.data);
    };

    fetchCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    if (getValues("country")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      if (countryObj) {
        const fetchStates = async () => {
          const response = await Client.get(
            `/location/states/${countryObj.isoCode}`,
            {
              params: { country: countryObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setStates(response.data.data);

          // Only reset dependent fields if not in edit mode or if country has changed
          if (!getValues("state")) {
            setValue("state", "");
            setValue("stateCode", "");
            setValue("district", "");
            setValue("city", "");
          }
        };
        fetchStates();
      }
    } else {
      setStates([]);
      // Always reset when country is cleared
      setValue("state", "");
      setValue("stateCode", "");
      setValue("district", "");
      setValue("city", "");
    }
  }, [getValues("country"), countries, setValue]);

  useEffect(() => {
    if (getValues("country") === "India" && getValues("state")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      const stateObj = states.find((s) => s.name === getValues("state"));
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);

          // Only reset district if not in edit mode or if state has changed
          if (!getValues("district")) {
            setValue("district", "");
          }
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
      // Always reset when state is cleared
      setValue("district", "");
    }
  }, [getValues("state"), countries, states]);

  // Load cities when state changes
  useEffect(() => {
    // Get current values
    const country = getValues("country");
    const state = getValues("state");

    if (country && state) {
      const countryObj = countries.find((c) => c.name === country);
      const stateObj = states.find((s) => s.name === state);
      if (countryObj && stateObj) {
        const fetchCities = async () => {
          const response = await Client.get(
            `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setCities(response.data.data);

          // Only reset city if not in edit mode or if state has changed
          if (!getValues("city")) {
            setValue("city", "");
          }
        };
        fetchCities();
      }
    } else {
      setCities([]);
      // Always reset when state or country is cleared
      if (!state) {
        setValue("city", "");
      }
      if (!country) {
        setValue("district", "");
        setValue("city", "");
      }
    }
  }, [countries, states, getValues("state")]);

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setIsLoading(true);
        const response = await Client.get(
          `/admin/details/single/club/${profileClubId}`
        );
        if (response.status === 204) {
          toast.info("You haven't created a profile yet.");
          return;
        }
        if (!response.data.success) {
          toast.info(response.data.message);
          return;
        }
        const clubDetails = response.data.data;

        // Process club details to handle null values
        const processedDetails = {
          ...clubDetails,
          // Convert null values to empty strings or appropriate defaults
          clubName: clubDetails.clubName || "",
          clubDistrictId: clubDetails.clubDistrictId || "",
          country: clubDetails.country || "",
          state: clubDetails.state || "",
          district: clubDetails.district || "",
          city: clubDetails.city || "",
          pincode: clubDetails.pincode || "",
          address: clubDetails.address || "",
          authorizedSignatoryName: clubDetails.authorizedSignatoryName || "",
          authorizedSignatoryContactNumber:
            clubDetails.authorizedSignatoryContactNumber || "",
          authorizedSignatoryEmail: clubDetails.authorizedSignatoryEmail || "",
          authorizedSignatoryDesignation:
            clubDetails.authorizedSignatoryDesignation || "",
          contactPersonName: clubDetails.contactPersonName || "",
          contactPersonNumber: clubDetails.contactPersonNumber || "",
          alternateContactNumber: clubDetails.alternateContactNumber || "",
          contactPersonEmail: clubDetails.contactPersonEmail || "",
          profileUrl: clubDetails.profileUrl || "",
          locationUrl: clubDetails.locationUrl || "",
          countryCode: clubDetails.countryCode || "",
          stateCode: clubDetails.stateCode || "",
          email: clubDetails.email || "",
          id: clubDetails.id || "",
        };

        // Reset with the processed details
        reset(processedDetails);
        setOriginalProfileData(processedDetails);

        // Then find and set the country code
        if (clubDetails.country && countries.length > 0) {
          const countryObj = countries.find(
            (c) => c.name === clubDetails.country
          );
          if (countryObj) {
            setValue("countryCode", countryObj.isoCode);

            // Fetch states for this country
            const statesResponse = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              { params: { country: countryObj.isoCode } }
            );

            if (statesResponse.data.success) {
              const statesData = statesResponse.data.data;
              setStates(statesData);

              // Find and set the state code
              if (clubDetails.state) {
                const stateObj = statesData.find(
                  (s) => s.name === clubDetails.state
                );
                if (stateObj) {
                  setValue("stateCode", stateObj.isoCode);

                  // Fetch cities for this state
                  const citiesResponse = await Client.get(
                    `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
                    {
                      params: {
                        country: countryObj.isoCode,
                        state: stateObj.isoCode,
                      },
                    }
                  );

                  if (citiesResponse.data.success) {
                    setCities(citiesResponse.data.data);
                  }
                }
              }
            }
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching club profile:", error);
        let errorMessage = "Error fetching club data";
        if (error.response) {
          errorMessage = error.response.data?.message || "Server error";
        } else if (error.request) {
          errorMessage = "No response from server";
        } else {
          errorMessage = error.message;
        }
        toast.error("Failed to fetch club profile: " + errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfileData();
  }, [countries, reset, setValue]);

  const onError = (errors) => {
    const firstField = Object.keys(errors)[0];
    const errorData = errors[firstField];

    const sectionElement = document.querySelector(`[name="${firstField}"]`);

    if (sectionElement) {
      const scrollOffset = 100;
      const y =
        sectionElement.getBoundingClientRect().top +
        window.pageYOffset -
        scrollOffset;
      window.scrollTo({ top: y, behavior: "smooth" });

      setTimeout(() => {
        if ("focus" in sectionElement) {
          sectionElement.focus();
        }
      }, 500);
    }

    toast.info(
      `Error in field: ${firstField} – ${errorData?.message || "Invalid input"}`
    );
  };
  // Form submission handler
  const onSubmit = async (data) => {
    setIsLoading(true);
    const dataForStorage = { ...data };
    delete dataForStorage.profileImage;
    delete dataForStorage.profileImagePreview; // Don't store preview in localStorage
    localStorage.setItem("playerProfileDraft", JSON.stringify(dataForStorage));

    try {
      const changedData = {};

      Object.keys(data).forEach((key) => {
        if (["profileImagePreview"].includes(key)) return;

        if (key === "profileImage") {
          if (data.profileImage && data.profileImage instanceof File) {
            changedData.profileImage = data.profileImage;
          }
          return;
        }

        if (
          JSON.stringify(data[key]) !== JSON.stringify(originalProfileData[key])
        ) {
          changedData[key] = data[key];
        }
      });

      if (Object.keys(changedData).length === 0) {
        toast.info("No changes detected to update.");
        return;
      }

      const formData = new FormData();

      Object.keys(changedData).forEach((key) => {
        const value = changedData[key];

        if (key === "profileImage" && value instanceof File) {
          formData.append("profileImage", value);
        } else if (typeof value === "object" && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, String(value));
        }
      });

      const response = await Client.put(
        `/admin/details/update/club/${originalProfileData.id}`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      handleResponse(response);
      return;
    } catch (error) {
      handleError(error);
    } finally {
      // setSubmitting(false);
    }
  };
  // Helper function to handle the API response
  const handleResponse = (response) => {
    if (!response.data.success) {
      if (response.data.zodErrors) {
        // Show the first Zod validation error
        const zodErrors = response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else {
        toast.error(response.data.error || "Unexpected error");
      }
      return;
    }

    toast.success(`Profile updated successfully`);

    // Clear draft from localStorage after successful submission
    localStorage.removeItem("playerProfileDraft");
    sessionStorage.removeItem("new_user");

    // Reset the form
    reset();

    // Navigate back to profile page
    navigate(`/dashboard/clubs/${profileClubId}`);
  };

  // Helper function to handle errors
  const handleError = (error) => {
    console.error(`Error updating profile:`, error);

    if (error.response?.data?.error?.message === "Incorrect OTP.") {
      toast.error("Incorrect OTP. Please try again.");
    } else if (error.response?.status === 422 && error.response?.data?.data) {
      const validationErrors = error.response.data.data;
      Object.keys(validationErrors).forEach((key) => {
        if (key !== "_errors" && validationErrors[key]._errors) {
          toast.error(
            `Validation failed - ${key}: ${validationErrors[key]._errors.join(
              ", "
            )}`
          );
        }
      });
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error.message || "An error occurred");
    } else if (error.response?.data?.zodErrors) {
      // Handle Zod validation errors from the server
      const zodErrors = error.response.data.zodErrors;
      const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
      toast.error(firstZodError || "Please correct the validation errors");
    } else {
      const errorMessage =
        error.response?.data?.message ||
        `An unexpected error occurred while updating the profile.`;
      toast.error(errorMessage);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton to={"/dashboard"} />

      <Paper elevation={3} sx={{ p: 4 }}>
        <form onSubmit={handleSubmit(onSubmit, onError)}>
          <Grid
            container
            spacing={3}
            sx={{
              ".MuiFormControl-root": { mt: "4px !important" },
              ".MuiInputBase-input": { py: 1.5 },
              ".MuiAutocomplete-input": { p: "4px !important" },
              ".MuiGrid-root .MuiGrid-item": { pt: "0px !important" },
              position: "relative",
            }}
          >
            {isLoading && (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  zIndex: 1000,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  width: "100%",
                  height: "100%",
                  backgroundColor: "rgba(255, 255, 255, 0.5)",
                }}
              >
                <Spinner />
              </Box>
            )}

            <Grid container item xs={12}>
              <Grid item xs={12} md={6} sx={{ mt: 2 }}>
                <Typography
                  variant="h4"
                  sx={{ mb: 4, textDecoration: "underline" }}
                >
                  Club Profile
                </Typography>

                <Grid item xs={12} md={6}>
                  <FormTextField
                    name="clubDistrictId"
                    control={control}
                    title="Club ID"
                    placeholder="Enter Club ID"
                  />
                </Grid>
              </Grid>
              <Grid
                item
                xs={12}
                md={6}
                sx={{ display: "flex", justifyContent: "end" }}
              >
                <ProfileImageUpload
                  control={control}
                  setValue={setValue}
                  watch={watch}
                />
              </Grid>
            </Grid>
            {/* Basic Information */}

            <Grid item xs={12}>
              <FormTextField
                name="clubName"
                control={control}
                maxLength={150}
                title="Club Name"
                placeholder="Enter Club Name"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="country"
                control={control}
                options={countries}
                getOptionLabel={(option) => option.name}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                title="Country"
                placeholder="Select Country"
                onChange={(_, newValue) => {
                  if (newValue) {
                    setValue("country", newValue.name);
                    setValue("countryCode", newValue.isoCode);

                    // Only reset dependent fields if not in edit mode or if country has changed
                    if (getValues("country") !== newValue.name) {
                      setValue("state", "");
                      setValue("stateCode", "");
                      setValue("city", "");
                    }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="state"
                control={control}
                options={states}
                getOptionLabel={(option) => option.name}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                title="State"
                placeholder="Select State"
                disabled={!countryCode}
                onChange={(_, newValue) => {
                  if (newValue) {
                    setValue("state", newValue.name);
                    setValue("stateCode", newValue.isoCode);

                    // Only reset city if not in edit mode or if state has changed
                    if (getValues("state") !== newValue.name) {
                      setValue("city", "");
                    }
                  }
                }}
              />
            </Grid>

            {getValues("country") === "India" && (
              <Grid item xs={12} md={6}>
                <FormAutocomplete
                  name="district"
                  control={control}
                  sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                  title="District"
                  placeholder="Select District"
                  options={districts}
                  getOptionLabel={(option) => option.name}
                  disabled={!stateCode || !countryCode}
                  onChange={(_, newValue) => {
                    if (newValue) {
                      setValue("district", newValue.name);
                    }
                  }}
                />
              </Grid>
            )}
            {(getValues("country") !== "India" ||
              getValues("country") === "") && (
              <Grid item xs={12} md={6}>
                <FormTextField
                  name="district"
                  control={control}
                  title="District"
                  placeholder="Enter District"
                />
              </Grid>
            )}

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="city"
                control={control}
                options={cities}
                getOptionLabel={(option) => option.name}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                title="City"
                placeholder="Select City"
                disabled={!stateCode || !countryCode}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormNumberField
                name="pincode"
                control={control}
                title="Pincode"
                placeholder="Enter Pincode"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  maxLength: 6,
                }}
              />
              <FormTextField
                name="address"
                control={control}
                title="Address"
                placeholder="Enter Address"
                multiline
                rows={3}
                maxLength={250}
                specialCharAllowed={true}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                sx={{
                  textAlign: "start",
                  p: "0px !important",
                  m: "0px !important",
                }}
              >
                Location
              </Typography>
              <AutoNavigateMapComponent
                control={control}
                name="locationUrl"
                countryField="country"
                stateField="state"
                cityField="city"
              />
            </Grid>

            {/* Authorized Signatory Information */}

            <Grid item xs={12} md={6}>
              <FormTextField
                name="authorizedSignatoryName"
                control={control}
                title="Authorized Signatory Name"
                placeholder="Enter Name"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormPhoneInput
                name="authorizedSignatoryContactNumber"
                control={control}
                title="Authorized Signatory Contact Number"
                placeholder="Enter Contact Number"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="authorizedSignatoryEmail"
                control={control}
                title="Authorized Signatory Email"
                placeholder="Enter Email"
                type="email"
                specialCharAllowed={true}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="authorizedSignatoryDesignation"
                control={control}
                title="Authorized Signatory Designation"
                placeholder="Enter Designation"
              />
            </Grid>

            {/* Contact Person Information */}

            <Grid item xs={12} md={6}>
              <FormTextField
                name="contactPersonName"
                control={control}
                title="Contact Person Name"
                placeholder="Enter Contact Person Name"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormPhoneInput
                name="contactPersonNumber"
                control={control}
                title="Contact Person Mobile Number"
                placeholder="Enter Contact Number"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormPhoneInput
                name="alternateContactNumber"
                control={control}
                title="Alternative Contact Number"
                placeholder="Enter Alternative Contact Number"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="contactPersonEmail"
                control={control}
                title="Contact Person Email ID"
                placeholder="Enter Email"
                type="email"
                specialCharAllowed={true}
              />
            </Grid>

            {/* <Box sx={{ mt: 6, width: "100%" }}>
          <Button
            variant="contained"
            startIcon={<Add />}
            sx={{
              fontSize: 16,
              px: 2,
              bgcolor: "hsla(242, 56%, 36%, 1)",
              "&:hover": {
                bgcolor: "rgb(37, 34, 110)",
              },
            }}
          >
            Add branch
          </Button>
        </Box> */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                width: "100%",
                mt: 4,
              }}
            >
              <Button
                type="submit"
                variant="contained"
                sx={{
                  fontSize: 16,
                  px: 2,
                  bgcolor: isValid
                    ? "hsla(120, 49%, 35%, 1)"
                    : "hsla(0, 3%, 80%, 1)",
                  "&:hover": {
                    bgcolor: isValid
                      ? "rgb(39, 104, 39)"
                      : "hsla(0, 3%, 80%, 1)",
                  },
                }}
                size="large"
              >
                Update
              </Button>
            </Box>
          </Grid>
        </form>
      </Paper>
    </Container>
  );
};

export default AdminClubProfileEdit;

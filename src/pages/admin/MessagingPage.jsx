import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Grid,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import PlayersSearchForm from "../../components/common/PlayersSearchForm";
import ClubSearchForm from "../../components/common/ClubSearchForm";
import ArbiterSearchForm from "../../components/form/ArbiterSearchForm";
import TournamentSearchForm from "../../components/common/TournamentSearchForm";
import SelectableTable from "../../components/admin/SelectableTable";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const MessagingPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();

  // Extract mode from URL path (email, sms, whatsapp)
  const mode = location.pathname.split("/").pop() || "email";

  const [selectedType, setSelectedType] = useState("player");
  const [searchResults, setSearchResults] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [search, setSearch] = useState({});
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [bulkLoading, setBulkLoading] = useState(false);
  const [bulkConfirmDialog, setBulkConfirmDialog] = useState(false);

  // Reset selected users when type changes
  useEffect(() => {
    setSelectedUsers([]);
    setSearchResults([]);
    setSearchPerformed(false);
    setSearch({});
    setPage(1);
  }, [selectedType]);

  const handleTypeChange = (event) => {
    setSelectedType(event.target.value);
  };

  const handleUserSelection = (users) => {
    setSelectedUsers(users);
  };

  // Utility function to ensure page is a number
  const normalizePage = (pageInput) => {
    if (typeof pageInput === "number") {
      return Math.max(1, Math.floor(pageInput));
    }
    if (typeof pageInput === "string") {
      const parsed = parseInt(pageInput, 10);
      return isNaN(parsed) ? 1 : Math.max(1, parsed);
    }
    if (typeof pageInput === "object" && pageInput !== null) {
      // If it's an object, try to extract page number
      if ("page" in pageInput) {
        return normalizePage(pageInput.page);
      }
      if ("current" in pageInput) {
        return normalizePage(pageInput.current);
      }
    }
    return 1;
  };

  // Search function that will be called by the search forms
  const handleSearch = async (pageNum = 1) => {
    setLoading(true);

    // Normalize page number to prevent object issues
    const normalizedPage = normalizePage(pageNum);
    setPage(normalizedPage);

    try {
      let endpoint = "";
      const baseParams = { page: normalizedPage, limit: 50 };
      let searchParams = {};

      const addIfExists = (key, value) => {
        if (
          value !== undefined &&
          value !== null &&
          value !== "" &&
          value !== 0
        ) {
          searchParams[key] = value;
        }
      };

      switch (selectedType) {
        case "player":
          endpoint = "/admin/users/player";
          addIfExists("playerName", search.playerName);
          addIfExists("playerId", search.playerId);
          break;
        case "club":
          endpoint = "admin/users/club";
          addIfExists("clubName", search.clubName);
          break;
        case "arbiter":
          endpoint = "admin/users/arbiter";
          addIfExists("arbiterName", search.arbiterName);
          addIfExists("arbiterId", search.arbiterId);
          break;
        // case "tournament":
        //   endpoint = "admin/user/tournament";
        //   addIfExists("title", search.title);
        //   addIfExists("tournamentType", search.tournamentType);
        //   addIfExists("tournamentCategory", search.tournamentCategory);
        //   addIfExists("age", search.age);
        //   addIfExists("month", search.month);
        //   addIfExists("year", search.year);
        //   break;
        default:
          toast.error("Invalid search type");
          setLoading(false);
          return;
      }

      // Common fields
      addIfExists("country", search.country);
      addIfExists("state", search.state);
      addIfExists("district", search.district);
      addIfExists("city", search.city);

      const params = { ...baseParams, ...searchParams };


      const response = await Client.get(endpoint, { params });

      if (response?.data?.success) {
        const responseData = response.data.data || {};
        const results =
          responseData.players ||
          responseData.clubs ||
          responseData.arbiters ||
          // responseData.tournaments ||
          [];

        setSearchResults(Array.isArray(results) ? results : []);
        setTotalPages(Math.max(1, response.data.totalPages || 1));
        setTotalCount(response.data.totalCount || results.length);
        setSearchPerformed(true);
        

        if (results.length === 0) {
          toast.info("No results found");
        }
      } else {
        const errorMessage = response?.data?.message || "Search failed";
        toast.error(errorMessage);
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Search error:", error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Search failed. Please try again.";
      toast.error(errorMessage);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch all users for bulk send
  const fetchAllUsers = async () => {
    setBulkLoading(true);

    try {
      let endpoint = "";
      let searchParams = {};

      const addIfExists = (key, value) => {
        if (
          value !== undefined &&
          value !== null &&
          value !== "" &&
          value !== 0
        ) {
          searchParams[key] = value;
        }
      };

      switch (selectedType) {
        case "player":
          endpoint = "/admin/users/player";
          addIfExists("playerName", search.playerName);
          addIfExists("playerId", search.playerId);
          break;
        case "club":
          endpoint = "admin/users/club";
          addIfExists("clubName", search.clubName);
          break;
        case "arbiter":
          endpoint = "admin/users/arbiter";
          addIfExists("arbiterName", search.arbiterName);
          addIfExists("arbiterId", search.arbiterId);
          break;
        // case "tournament":
        //   endpoint = "admin/user/tournament";
        //   addIfExists("title", search.title);
        //   addIfExists("tournamentType", search.tournamentType);
        //   addIfExists("tournamentCategory", search.tournamentCategory);
        //   addIfExists("age", search.age);
        //   addIfExists("month", search.month);
        //   addIfExists("year", search.year);
        //   break;
        default:
          toast.error("Invalid search type");
          setBulkLoading(false);
          return [];
      }

      // Common fields
      addIfExists("country", search.country);
      addIfExists("state", search.state);
      addIfExists("district", search.district);
      addIfExists("city", search.city);

      // Fetch all users without pagination
      const params = { ...searchParams, limit: 10000, page: 1 };


      const response = await Client.get(endpoint, { params });

      if (response?.data?.success) {
        const responseData = response.data.data || {};
        const results =
          responseData.players ||
          responseData.clubs ||
          responseData.arbiters ||
          // responseData.tournaments ||
          [];

        return Array.isArray(results) ? results : [];
      } else {
        const errorMessage =
          response?.data?.message || "Failed to fetch all users";
        toast.error(errorMessage);
        return [];
      }
    } catch (error) {
      console.error("Bulk fetch error:", error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to fetch all users. Please try again.";
      toast.error(errorMessage);
      return [];
    } finally {
      setBulkLoading(false);
    }
  };

  // Handle bulk send confirmation
  const handleBulkSendConfirm = async () => {
    setBulkConfirmDialog(false);

    const allUsers = await fetchAllUsers();

    if (allUsers.length === 0) {
      toast.error("No users found for bulk messaging");
      return;
    }

    // Extract relevant information based on selected type
    const extractedInfo = allUsers.map((user) => {
    
      const baseInfo = {
        id:
          user.id ||
          user.userId ||
          user.id ||
          user.cbid ||
          user.clubId ||
          "",
        name: user.name || user.playerName || user.clubName || "",
        email: user.email || user.User?.email || "",
        phone: user.phoneNumber || user.mobile || user.User?.phoneNumber || "",
        city: user.city || "",
        state: user.state || "",
        country: user.country || "",
        district: user.district || "",
        profileUrl: user.profileUrl || "",
      };

      // Add type-specific information
      switch (selectedType) {
        case "player":
          return {
            ...baseInfo,
            cbid: user.cbid || "",
            fideId: user.fideId || "",
            aicfId: user.aicfId || user.icfId || "",
            fideRating: user.fideRating || "",
            playerTitle: user.playerTitle || user.title || "",
            districtId: user.districtId || "",
            stateId: user.stateId || "",
            type: "player",
          };

        case "club":
          return {
            ...baseInfo,
            clubId: user.clubId || "",
            address: user.address || "",
            pincode: user.pincode || "",
            locationUrl: user.locationUrl || "",
            alternateContactNumber: user.alternateContactNumber || "",
            contactPersonName: user.contactPersonName || "",
            contactPersonEmail: user.contactPersonEmail || "",
            contactPersonNumber: user.contactPersonNumber || "",
            authorizedSignatoryName: user.authorizedSignatoryName || "",
            authorizedSignatoryEmail: user.authorizedSignatoryEmail || "",
            authorizedSignatoryDesignation:
              user.authorizedSignatoryDesignation || "",
            authorizedSignatoryContactNumber:
              user.authorizedSignatoryContactNumber || "",
            clubDistrictId: user.clubDistrictId || "",
            type: "club",
          };

        case "arbiter":
          return {
            ...baseInfo,
            cbid: user.cbid || "",
            fideId: user.fideId || "",
            aicfId: user.aicfId || user.icfId || "",
            title: user.title || user.playerTitle || "",
            districtId: user.districtId || "",
            stateId: user.stateId || "",
            type: "arbiter",
          };

        case "tournament":
          return {
            ...baseInfo,
            tournamentId: user.tournamentId || "",
            type: "tournament",
          };

        default:
          return {
            ...baseInfo,
            type: selectedType,
          };
      }
    });

    // Optional: Helper function to clean up empty fields
    const cleanExtractedInfo = extractedInfo.map((user) => {
      const cleaned = {};
      Object.keys(user).forEach((key) => {
        if (user[key] !== "" && user[key] !== null && user[key] !== undefined) {
          cleaned[key] = user[key];
        }
      });
      return cleaned;
    });

    // Navigate to compose page with all users data
    navigate(`/dashboard/${mode}/compose`, {
      state: {
        selectedUsers: cleanExtractedInfo,
        selectedType,
        mode,
        isBulkSend: true,
        searchFilters: search,
      },
    });
  };

  // Reset function for search forms
  const handleReset = () => {
    setSearch({});
    setSearchResults([]);
    setSelectedUsers([]);
    setSearchPerformed(false);
    setPage(1);
  };

  const handleProceedToCompose = () => {
    if (selectedUsers.length === 0) {
      toast.error("Please select at least one recipient");
      return;
    }

    try {
      // Extract relevant information based on selected type
      const extractedInfo = selectedUsers.map((user) => {
        const baseInfo = {
          id: user.User.id || user.cbid || user.clubId || "",
          name: user.name || user.playerName || user.clubName || "",
          email: user.email || "",
          phone: user.phoneNumber || user.mobile || "",
        };

        // Add type-specific information
        switch (selectedType) {
          case "player":
            return {
              ...baseInfo,
              cbid: user.cbid || "",
              fideId: user.fideId || "",
              aicfId: user.aicfId || "",
              type: "player",
            };
          case "club":
            return {
              ...baseInfo,
              clubId: user.clubId || "",
              type: "club",
            };
          // case "tournament":
          //   return {
          //     ...baseInfo,
          //     tournamentId: user.tournamentId || '',
          //     type: "tournament",
          //   };
          default:
            return {
              ...baseInfo,
              type: selectedType,
            };
        }
      });

      // Navigate to compose page with selected users data
      navigate(`/dashboard/${mode}/compose`, {
        state: {
          selectedUsers: extractedInfo,
          selectedType,
          mode,
        },
      });
    } catch (error) {
      console.error("Error proceeding to compose:", error);
      toast.error("Failed to proceed to compose page. Please try again.");
    }
  };

  const getPageTitle = () => {
    const modeTitle = mode.charAt(0).toUpperCase() + mode.slice(1);
    return `${modeTitle} Messaging`;
  };

  const getTypeOptions = () => [
    { value: "player", label: "Players" },
    { value: "club", label: "Clubs" },
    { value: "arbiter", label: "Arbiters" },
    // { value: "tournament", label: "Tournaments" },
  ];

  const getTypeLabelSingular = () => {
    switch (selectedType) {
      case "player":
        return "players";
      case "club":
        return "clubs";
      case "arbiter":
        return "arbiters";
      // case "tournament": return "tournaments";
      default:
        return "users";
    }
  };

  // Render the appropriate search form based on selected type
  const renderSearchForm = () => {
    const commonProps = {
      search,
      setSearch,
      handleSearch,
      loading,
      handleReset,
      adminSearch: true,
    };

    switch (selectedType) {
      case "player":
        return <PlayersSearchForm {...commonProps} />;
      case "club":
        return <ClubSearchForm {...commonProps} />;
      case "arbiter":
        return <ArbiterSearchForm {...commonProps} />;
      // case "tournament":
      //   return <TournamentSearchForm {...commonProps} />;
      default:
        return <PlayersSearchForm {...commonProps} />;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />

      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          {getPageTitle()}
        </Typography>
        <Typography sx={{ fontSize: "16px", textAlign: "start" }} color="black">
          Select recipients and compose your {mode} message
        </Typography>
      </Box>

      {/* Type Selection */}
      <Box>
        <Typography variant="h6" gutterBottom>
          Select Recipient Type
        </Typography>
        <FormControl fullWidth sx={{ maxWidth: 300 }}>
          <Select
            value={selectedType}
            onChange={handleTypeChange}
            sx={{ "& .MuiSelect-select": { padding: "8px 14px" } }}
          >
            {getTypeOptions().map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <Box sx={{ my: 2 }}>{renderSearchForm()}</Box>

      {/* Bulk Send Option */}
      {searchPerformed && totalCount > 0 && (
        <Paper sx={{ p: 3, mb: 3, bgcolor: "#f5f5f5" }}>
          <Typography variant="h6" gutterBottom>
            Bulk Messaging Option
          </Typography>
          <Alert severity="info" sx={{ mb: 2, fontSize: "16px" }}>
            Found {totalCount} {getTypeLabelSingular()} matching your search
            criteria. You can send messages to all of them at once using bulk
            send.
          </Alert>
          <Button
            variant="outlined"
            color="primary"
            size="large"
            onClick={() => setBulkConfirmDialog(true)}
            disabled={bulkLoading}
            sx={{ mr: 2 }}
          >
            {bulkLoading
              ? "Loading..."
              : `Send to All ${totalCount} ${
                  getTypeLabelSingular().charAt(0).toUpperCase() +
                  getTypeLabelSingular().slice(1)
                }`}
          </Button>
          <Typography
            variant="body2"
            color="black"
            sx={{ mt: 1, fontSize: 16 }}
          >
            This will send the message to all {getTypeLabelSingular()} matching
            your current search filters.
          </Typography>
        </Paper>
      )}

      {/* Results Table */}
      {searchPerformed && (
        <SelectableTable
          data={searchResults}
          selectedType={selectedType}
          onSelectionChange={handleUserSelection}
          loading={loading}
          page={page}
          totalPages={totalPages}
          onPageChange={handleSearch}
        />
      )}

      {/* Selected Users Summary */}
      {selectedUsers.length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Selected Recipients ({selectedUsers.length})
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
            {selectedUsers.slice(0, 10).map((user, index) => (
              <Chip
                key={user.id || user.cbid || user.clubId || index}
                label={
                  user.name ||
                  user.playerName ||
                  user.clubName ||
                  `User ${index + 1}`
                }
                variant="outlined"
                size="small"
              />
            ))}
            {selectedUsers.length > 10 && (
              <Chip
                label={`+${selectedUsers.length - 10} more`}
                variant="outlined"
                size="small"
                color="primary"
              />
            )}
          </Box>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleProceedToCompose}
            sx={{ mt: 2 }}
          >
            Proceed to Compose {mode.toUpperCase()}
          </Button>
        </Paper>
      )}

      {/* Bulk Send Confirmation Dialog */}
      <Dialog
        open={bulkConfirmDialog}
        onClose={() => setBulkConfirmDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Confirm Bulk {mode.toUpperCase()} Send</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to send {mode} messages to all {totalCount}{" "}
            {getTypeLabelSingular()}
            matching your search criteria?
            {Object.keys(search).length > 0 && (
              <Box sx={{ mt: 2, p: 2, bgcolor: "#f5f5f5", borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Applied Filters:
                </Typography>
                {Object.entries(search).map(
                  ([key, value]) =>
                    value && (
                      <Typography key={key} variant="body2">
                        {key}: {value}
                      </Typography>
                    )
                )}
              </Box>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkConfirmDialog(false)}>Cancel</Button>
          <Button
            onClick={handleBulkSendConfirm}
            variant="contained"
            color="primary"
            disabled={bulkLoading}
          >
            {bulkLoading ? "Loading..." : "Confirm Bulk Send"}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default MessagingPage;
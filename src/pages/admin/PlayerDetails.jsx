import React, { useEffect, useState } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import { Person, Add, PersonAdd } from "@mui/icons-material";
import { useNavigate, useParams } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import { DetailTable } from "../../components/common/DetailTable";
import ClubInvitationModal from "../../components/club/ClubInvitationModal";
import FriendRequestModal from "../../components/player/FriendRequestModal";
import BackButton from "../../components/common/BackButton";
import CloseIcon from "@mui/icons-material/Close";
import CheckIcon from "@mui/icons-material/Check";
import EditIcon from "@mui/icons-material/Edit";
import BlockIcon from "@mui/icons-material/Block";
import GppBadIcon from "@mui/icons-material/GppBad"; // alternative to Shield
import GppGoodIcon from "@mui/icons-material/GppGood"; // alternative to ShieldOff
import DeleteIcon from "@mui/icons-material/Delete";
import SmsIcon from "@mui/icons-material/Sms";
import EmailIcon from "@mui/icons-material/Email";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import AdminBanVerificationPopup from "../../components/admin/Banpopup";
import ConfirmationPopup from "../../components/admin/ConfirmationPopup";

const PlayerDetails = () => {
  const [playerInfo, setPlayerInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAccess, setIsAccess] = useState(true);
  const [isActive, setIsActive] = useState(true);
  const [invitationModalOpen, setInvitationModalOpen] = useState(false);
  const [friendRequestModalOpen, setFriendRequestModalOpen] = useState(false);
  const [isFriend, setIsFriend] = useState(false);
  const toast = UseToast();
  const { user, isLoggedIn, currentProfile } = UseGlobalContext();
  const navigate = useNavigate();
  const [isMember, setIsMember] = useState(false);
  const { id: cbid } = useParams();

  const [open, setOpen] = useState(false)
  const [openPopup, setOpenPopup] = useState(false)


  const handlePlayerRemove = async () => {
    try {
      const response = await Client.post(`/club/remove-player`, {
        playerId: playerInfo?.cbid,
      });
      if (response.data.success) {
        toast.success("Player removed successfully");
        setIsMember(false);
      } else {
        toast.error(response.data.message || "Failed to remove player");
      }
    } catch (error) {
      console.error("Error removing player:", error);
    }
  };

  useEffect(() => {
    if (
      currentProfile &&
      currentProfile?.id === playerInfo?.PlayerDetail?.clubId
    ) {
      setIsMember(true);
    } else {
      setIsMember(false);
    }
  }, [currentProfile, playerInfo]);

  // Fetch player information
  // Check if the player is already a friend
  const checkFriendStatus = async (playerId) => {
    if (!isLoggedIn || user?.role !== "player") return;

    try {
      const response = await Client.get(
        `/user/check-friend-status/${playerId}`
      );
      if (response.data.success) {
        setIsFriend(response.data.isFriend);
      }
    } catch (error) {
      console.error("Error checking friend status:", error);
    }
  };
  const handleRemoveFriend = async () => {
    try {
      const response = await Client.post("/user/friend/remove", {
        friendId: playerInfo.id,
      });
      if (response.data.success) {
        toast.success("Friend removed successfully");
        setIsFriend(false);
      } else {
        toast.error(response.data.message || "Failed to remove friend");
      }
    } catch (error) {
      console.error("Error removing friend:", error);
      toast.error("An error occurred while removing friend");
    }
  };

  const handleActive = async (value) => {
    try {

      const response = await Client.patch("/admin/access/is-active", {
        value: value,
        id: cbid,
      });

      if (response.data.success) {
        toast.success("player blocked successfully");
        setIsActive(response.data.data);
      }
    } catch (error) {
      console.error("Error", error);
      toast.error("An error occurred while updating player block");
    }
  };
  const handleUnActive = async () => {
    try {

      // return
      const response = await Client.post("/admin/access/ban", {
        value: true,
        userId: playerInfo.id,
      });

      if (response.data.success) {
        toast.success("player Unblocked successfully");
        setIsActive(response.data.data);
        fetchPlayerInfo();
      }
    } catch (error) {
      console.error("Error", error);
      toast.error("An error occurred while updating player block");
    }
  };

  const fetchPlayerInfo = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await Client.get(`/admin/details/single/${cbid}`);
      if (response.status === 204) {
        toast.info("Player not found");
        navigate(-1);
        return;
      }
      if (!response.data.success) {
        toast.error(response.data.message);
        setError(response.data.message);
        return;
      }

      const playerData = response.data.data;
      setPlayerInfo(playerData);

      setIsActive(playerData.isActive)
      setIsAccess(playerData.isAccess)


      // Check if player is a friend
      if (playerData.id) {
        checkFriendStatus(playerData.id);
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching player info:", error);
      setError("Error fetching player info");
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchPlayerInfo();
  }, [cbid]);

  // Format player data for display
  const formattedPlayerData = formatPlayerData(playerInfo);

  const actionButtons = [
    // { label: 'Allow', action: 'Allow Access', color: 'success', icon: <CheckIcon fontSize="small" />,variant:isAccess ?'contained':'outlined' },
    // { label: 'Deny', action: 'Deny Access', color: 'error', icon: <CloseIcon fontSize="small" /> ,variant:isAccess ?'outlined':'contained'},

    { label: 'Edit', action: 'Edit', color: 'primary', icon: <EditIcon fontSize="small" />, variant: 'contained' },
    { label: 'Block', action: 'Block', color: 'warning', icon: <GppBadIcon fontSize="small" />, variant: 'contained', disable: isAccess ? false : true },
    { label: 'Unblock', action: 'Unblock', color: 'warning', icon: <GppGoodIcon fontSize="small" />, variant: 'contained', disable: isAccess ? true : false },
    { label: 'Archive', action: 'Archive', color: 'error', icon: <DeleteIcon fontSize="small" />, variant: 'contained', disable: isActive ? false : true },
    { label: 'UnArchive', action: 'UnArchive', color: 'error', icon: <DeleteIcon fontSize="small" />, variant: 'contained', disable: isActive ? true : false },

    // { label: 'SMS', action: 'SMS', color: 'secondary', icon: <SmsIcon fontSize="small" />,variant:'contained' },
    {
      label: "Email",
      action: "Email",
      color: "info",
      icon: <EmailIcon fontSize="small" />,
      variant: "contained",
    },
    // { label: 'WhatsApp', action: 'WhatsApp', color: 'success', icon: <WhatsAppIcon fontSize="small" />,variant:'contained' },
  ];

  const handleMail = () => {
    navigate(`/dashboard/email/compose`, {
      state: {
        selectedUsers: [playerInfo],
        selectedType: "player",
        mode: "mail",
      },
    });
  };
  const handleAction = (action) => {
    switch (action) {
      case "Allow Access":
        // TODO: Implement Allow logic
        break;
      case "Deny Access":
        // TODO: Implement Deny logic
        break;
      case "Edit":
        navigate(`/dashboard/players/${playerInfo.id}/edit`, {
          state: {
            userId: playerInfo.id,
            playerId: playerInfo.PlayerDetail.id,
            cbid: playerInfo.cbid,
          },
        });
        break;

      case 'Block':
        setOpenPopup(true)
        break;
      case 'Unblock':
        setOpenPopup(true)
        break;
      case 'Archive':
        setOpen(true)
        break;
      case 'UnArchive':
        handleUnActive()
        break;
      case "SMS":
        // TODO: Send SMS logic
        break;
      case "Email":
        handleMail();
        break;
      case "WhatsApp":
        // TODO: WhatsApp integration logic
        break;
      default:
        console.warn("Unknown action:", action);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      <Paper
        elevation={3}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
          mb: 4,
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            flexDirection: { xs: "column", sm: "row" },
            flexFlow: "row",
            px: { xs: "4vw", sm: "10vw" },
            py: 2,
            borderBottom: "1px solid #f0f0f0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              alignSelf: "flex-start",
              gap: 2,
              width: "100%",
            }}
          >
            <Avatar
              src={playerInfo?.PlayerDetail?.profileUrl || ""}
              sx={{
                width: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                height: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                bgcolor: "#f5f5f5",
                color: "#000",
              }}
            >
              {!playerInfo?.PlayerDetail?.profileUrl && (
                <Person sx={{ fontSize: { xs: 50, md: 60 } }} />
              )}
            </Avatar>
            <Stack sx={{ width: "100%", textAlign: "start" }}>
              {!loading ? (
                <>
                  <Typography variant="h4" component="h4" fontWeight="500">
                    {formattedPlayerData.title}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="h6"
                    fontWeight="400"
                    sx={{ fontSize: { xs: "14px", sm: "16px" } }}
                  >
                    CBID: {playerInfo?.cbid || "-"}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="h6"
                    fontWeight="400"
                    sx={{ fontSize: { xs: "14px", sm: "16px" } }}
                  >
                    FIDE Rating: {playerInfo?.PlayerDetail?.fideRating || "-"}
                  </Typography>
                </>
              ) : (
                <Skeleton variant="text" width={200} />
              )}
            </Stack>
          </Box>
          {isLoggedIn &&
            user?.role === "club" &&
            (currentProfile &&
              currentProfile?.id === playerInfo?.PlayerDetail?.clubId ? (
              <Button
                variant="contained"
                color="success"
                startIcon={<Add />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,
                  backgroundColor: "hsla(132, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(33, 107, 48)" },
                  px: 4,

                  textWrap: "nowrap",
                }}
                onClick={handlePlayerRemove}
              >
                Remove Member
              </Button>
            ) : (
              <Button
                variant="contained"
                color="success"
                startIcon={<Add />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,
                  backgroundColor: "hsla(132, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(33, 107, 48)" },
                  px: 2,
                  textWrap: "nowrap",
                }}
                onClick={() => setInvitationModalOpen(true)}
              >
                Add Member
              </Button>
            ))}

          {isLoggedIn &&
            user?.role === "player" &&
            playerInfo?.id !== user?.userId &&
            (isFriend ? (
              <Button
                variant="contained"
                color="error"
                startIcon={<PersonAdd />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,

                  px: 4,
                  textWrap: "nowrap",
                }}
                onClick={handleRemoveFriend}
              >
                Remove Friend
              </Button>
            ) : (
              <Button
                variant="contained"
                color="success"
                startIcon={<PersonAdd />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,
                  backgroundColor: "hsla(132, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(33, 107, 48)" },
                  px: 2,
                  textWrap: "nowrap",
                }}
                onClick={() => setFriendRequestModalOpen(true)}
              >
                Add Friend
              </Button>
            ))}
        </Box>

        {/* Player Information */}
        <Box>
          {loading ? (
            // Loading skeleton
            Array(6)
              .fill(0)
              .map((_, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" height={40} />
                </Box>
              ))
          ) : error ? (
            // Error message
            <Typography color="error" variant="h6" align="center">
              {error}
            </Typography>
          ) : (
            // Player details table
            <DetailTable
              details={formattedPlayerData.details}
              rowColor={{ odd: "#A5D8C626", even: "#BEDDF04F" }}
            />
          )}
        </Box>
      </Paper>

      {/* Club Invitation Modal */}
      <ClubInvitationModal
        open={invitationModalOpen}
        onClose={() => setInvitationModalOpen(false)}
        playerData={playerInfo}
      />

      {/* Friend Request Modal */}
      <FriendRequestModal
        open={friendRequestModalOpen}
        onClose={() => setFriendRequestModalOpen(false)}
        playerData={playerInfo}
      />

      <Stack
        sx={{ alignItems: "center", display: "flex", justifyContent: "center" }}
        direction="row"
        spacing={2}
        flexWrap="wrap"
      >
        {actionButtons.map(
          ({ label, action, color, icon, variant, disable }) => (
            <Button
              key={label}
              variant={variant}
              color={color}
              disabled={disable}
              size="small"
              onClick={() => handleAction(action)}
              startIcon={icon}
              sx={{
                textTransform: "none",
                fontWeight: 500,
                mb: "15px !important",
              }}
            >
              {label}
            </Button>
          )
        )}
      </Stack>
      {open && (
        <AdminBanVerificationPopup
          open={open}
          onClose={() => setOpen(false)}
          User={playerInfo}
          isActive={isActive}
          onSuccess={fetchPlayerInfo}
        />
      )}

      {openPopup && (
        <ConfirmationPopup
          open={openPopup}
          onClose={() => setOpenPopup(false)}
          title="Do you really want to proceed?"
          apiEndpoint="/admin/access/archive"
          onSuccess={() => fetchPlayerInfo()}
          value={isAccess}
          Info={playerInfo}
        />)}
    </Container>
  );
};

export default PlayerDetails;

/**
 * Format player data for display in the detail table
 * @param {Object} player - Player data from API
 * @returns {Object} Formatted player data with title and details
 */
function formatPlayerData(player) {
  if (!player || !player.PlayerDetail) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const playerDetails = [
    // {
    //   label: "Full Name",
    //   value: player.name || "-",
    // },
    // {
    //   label: "CBID",
    //   value: player.cbid || "-",
    // },
    {
      label: "FIDE ID",
      value: player.PlayerDetail.fideId || "-",
    },
    {
      label: "AICF ID",
      value: player.PlayerDetail.aicfId || "-",
    },
    {
      label: "State ID",
      value: player.PlayerDetail.stateId || "-",
    },
    {
      label: "District ID",
      value: player.PlayerDetail.districtId || "-",
    },
    // {
    //   label: "FIDE Rating",
    //   value: player.PlayerDetail.fideRating || "-",
    // },
    {
      label: "Club",
      value: player.PlayerDetail.club || "-",
    },
    {
      label: "Country",
      value: player.PlayerDetail.country || "-",
    },
    {
      label: "State",
      value: player.PlayerDetail.state || "-",
    },
    {
      label: "District",
      value: player.PlayerDetail.district || "-",
    },
    {
      label: "new",
      value: "-",
    },
  ];

  const details = [
    { label: "Player Title", value: player.PlayerDetail.playerTitle || "-" },
    { label: "Player Name", value: player.name || "-" },
    { label: "DOB", value: player.PlayerDetail.dob || "-" },
    { label: "Mobile Number", value: player.phoneNumber || "-" },
    {
      label: "Alternate Mobile Number",
      value: player.PlayerDetail.alternateContact || "-",
    },
    { label: "eMail ID", value: player.email || "-" },
    {
      label: "Parent/Guardian Name",
      value: player.PlayerDetail.parentGuardianName || "-",
    },
    {
      label: "Emergency Contact",
      value: player.PlayerDetail.emergencyContact || "-",
    },
    { label: "FIDE Rating", value: player.PlayerDetail.fideRating || "-" },
    { label: "FIDE ID", value: player.PlayerDetail.fideId || "-" },
    { label: "AICF ID", value: player.PlayerDetail.aicfId || "-" },
    { label: "STATE ID", value: player.PlayerDetail.stateId || "-" },
    { label: "DISTRICT ID", value: player.PlayerDetail.districtId || "-" },
    { label: "Association", value: player.PlayerDetail.association || "-" },
    { label: "Club", value: player.PlayerDetail.club || "-" },
    { label: "Address", value: player.PlayerDetail.address || "-" },
    { label: "City", value: player.PlayerDetail.city || "-" },
    { label: "Pincode", value: player.PlayerDetail.pincode || "-" },
    { label: "District", value: player.PlayerDetail.district || "-" },
    { label: "State", value: player.PlayerDetail.state || "-" },
    { label: "Country", value: player.PlayerDetail.country || "-" },
    { label: "Referred By", value: player.referral_id || "-" },
    {
      label: "SignUp Date",
      value: player.PlayerDetail.createdAt.split("T")[0]
    }

  ];

  return {
    title: player.name || "",
    details: details,
  };
}

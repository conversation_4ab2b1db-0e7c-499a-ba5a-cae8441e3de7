import React, { useEffect, useState } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import { Person, Add } from "@mui/icons-material";
import { useNavigate, useParams } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import { DetailTable } from "../../components/common/DetailTable";
import ClubInvitationModal from "../../components/club/ClubInvitationModal";
import BackButton from "../../components/common/BackButton";
import CloseIcon from '@mui/icons-material/Close';
import CheckIcon from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import BlockIcon from '@mui/icons-material/Block';
import GppBadIcon from '@mui/icons-material/GppBad'; // alternative to Shield
import GppGoodIcon from '@mui/icons-material/GppGood'; // alternative to ShieldOff
import DeleteIcon from '@mui/icons-material/Delete';
import SmsIcon from '@mui/icons-material/Sms';
import EmailIcon from '@mui/icons-material/Email';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import AdminBanVerificationPopup from "../../components/admin/Banpopup";
import ConfirmationPopup from "../../components/admin/ConfirmationPopup";

const ArbiterDetails = () => {
  const [arbiterInfo, setArbiterInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [invitationModalOpen, setInvitationModalOpen] = useState(false);
  const toast = UseToast();
  const { id: cbid } = useParams();
  const [isAccess,setIsAccess]=useState(true);
  const [isActive,setIsActive]=useState(true);
  const [open,setOpen]=useState(false)
  const [openPopup, setOpenPopup] = useState(false)

  const navigate = useNavigate();
  // Fetch arbiter information
  const fetchArbiterInfo = async () => {
      setLoading(true);

      setError(null);
      try {
        const response = await Client.get(`/admin/details/single/arbiter/${cbid}`);
        if (response.status === 204) {
          toast.info("Arbiter not found");
          navigate(-1);
          return;
        }
        if (!response.data.success) {
          toast.error(response.data.message);
          setError(response.data.message);
          return;
        }
        setArbiterInfo(response.data.data);
        setLoading(false);
        setIsActive(response.data.data.isActive)
        setIsAccess(response.data.data.isAccess)
      } catch (error) {
        console.error("Error fetching arbiter info:", error);
        setError("Error fetching arbiter info");
        setLoading(false);
      }
    };

  useEffect(() => {
    fetchArbiterInfo();
  }, [navigate]);

  // Format arbiter data for display
  
  const formattedArbiterData = formatArbiterData(arbiterInfo);
 const handleUnActive= async()=>{
      try{
       
        // return
      const response = await Client.post('/admin/access/ban', {
          value: true,
          userId:arbiterInfo.id
        });

        if(response.data.success){
           toast.success("arbiter Unblocked successfully");
           setIsActive(response.data.data)
           fetchArbiterInfo()
        }
      }catch(error){
        console.error("Error", error);
        toast.error("An error occurred while updating player block");
      }
    }

    const actionButtons = [
  // { label: 'Allow', action: 'Allow Access', color: 'success', icon: <CheckIcon fontSize="small" />,variant:isAccess ?'contained':'outlined' },
  // { label: 'Deny', action: 'Deny Access', color: 'error', icon: <CloseIcon fontSize="small" /> ,variant:isAccess ?'outlined':'contained'},
  { label: 'Edit', action: 'Edit', color: 'primary', icon: <EditIcon fontSize="small" />,variant:'contained' },
  { label: 'Block', action: 'Block', color: 'warning', icon: <GppBadIcon fontSize="small" />,variant:'contained',disable:isAccess ?false:true},
  { label: 'Unblock', action: 'Unblock', color: 'warning', icon: <GppGoodIcon fontSize="small" />,variant:'contained',disable:isAccess ?true:false },
   { label: 'Archive', action: 'Archive', color: 'error', icon: <DeleteIcon fontSize="small" />,variant: 'contained', disable: isActive ? false : true},
    { label: 'UnArchive', action: 'UnArchive', color: 'error', icon: <DeleteIcon fontSize="small" />,variant: 'contained', disable: isActive ? true : false },
  // { label: 'SMS', action: 'SMS', color: 'secondary', icon: <SmsIcon fontSize="small" />,variant:'contained' },
  { label: 'Email', action: 'Email', color: 'info', icon: <EmailIcon fontSize="small" />,variant:'contained' },
  // { label: 'WhatsApp', action: 'WhatsApp', color: 'success', icon: <WhatsAppIcon fontSize="small" />,variant:'contained' },
];

  const handleMail=()=>{
    navigate(`/dashboard/email/compose`, {
        state: {
          selectedUsers: [arbiterInfo],
          selectedType:"arbiter",
          mode:'mail',
        },
      });}
const handleAction = (action) => {
  switch (action) {
    case 'Allow Access':

      // TODO: Implement Allow logic
      break;
    case 'Deny Access':

      // TODO: Implement Deny logic
      break;
    case 'Edit':
      navigate(`/dashboard/arbiter-profile/edit/${arbiterInfo.cbid}`);
      break;
    case 'Block':
        setOpenPopup(true)
      break;
    case 'Unblock':
        setOpenPopup(true)
      break;
   case 'Archive':
        setOpen(true)
        break;
      case 'UnArchive':
        handleUnActive()
        break;
    case 'SMS':
      // TODO: Send SMS logic
      break;
    case 'Email':
      handleMail()
      break;
    case 'WhatsApp':
      // TODO: WhatsApp integration logic
      break;
    default:
      console.warn("Unknown action:", action);
  }
};
  return (
    <Container maxWidth="xl" sx={{ py: 4,pt: 2 }}>
      <BackButton />
      <Paper
        elevation={3}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
          mb: 4,
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            flexDirection: { xs: "column", sm: "row" },
            flexFlow: "row",
            px: { xs: "4vw", sm: "10vw" },
            py: 2,
            borderBottom: "1px solid #f0f0f0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              alignSelf: "flex-start",
              gap: 2,
              width: "100%",
            }}
          >
            <Avatar
              src={arbiterInfo?.ArbiterDetail?.profileUrl}
              sx={{
                width: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                height: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                bgcolor: "#f5f5f5",
                border: "1px solid #00000030",
                color: "#000",
              }}
            >
              {!arbiterInfo?.ArbiterDetail?.profileUrl && (
                <Person sx={{ fontSize: { xs: 50, md: 60 } }} />
              )}
            </Avatar>
            <Stack sx={{ width: "100%", textAlign: "start" }}>
              {!loading ? (
                <>
                  <Typography variant="h4" component="h4" fontWeight="500">
                    {arbiterInfo?.name || "-"}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="h6"
                    fontWeight="400"
                    sx={{ fontSize: { xs: "14px", sm: "16px" } }}
                  >
                    CBID: {arbiterInfo?.cbid || "-"}
                  </Typography>
                </>
              ) : (
                <Skeleton variant="text" width={200} />
              )}
            </Stack>
          </Box>
        </Box>

        {/* Arbiter Information */}
        <Box>
          {loading ? (
            // Loading skeleton
            Array(6)
              .fill(0)
              .map((_, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" height={40} />
                </Box>
              ))
          ) : error ? (
            // Error message
            <Typography color="error" variant="h6" align="center">
              {error}
            </Typography>
          ) : (
            // Arbiter details table
            <DetailTable
              details={formattedArbiterData.details}
              rowColor={{ odd: "#A5D8C626", even: "#BEDDF04F" }}
            />
          )}
        </Box>
      </Paper>

      {/* Club Invitation Modal */}
      <ClubInvitationModal
        open={invitationModalOpen}
        onClose={() => setInvitationModalOpen(false)}
        arbiterData={arbiterInfo}
      />


      <Stack mt={5} sx={{alignItems:'center',display:'flex',justifyContent:"center"}} direction="row" spacing={2} flexWrap="wrap">
        {actionButtons.map(({ label, action, color, icon,variant,disable }) => (
          <Button
            key={label}
            variant={variant}
            disabled={disable}
            color={color}
            size="small"
            onClick={() => handleAction(action)}
            startIcon={icon}
            sx={{ textTransform: 'none', fontWeight: 500,mb:'15px !important' }}
          >
            {label}
          </Button>
        ))}
      </Stack>
 {open &&(
  <AdminBanVerificationPopup
  open={open}
  onClose={()=>setOpen(false)}
  User={arbiterInfo}
  isActive={isActive}
  onSuccess={fetchArbiterInfo}
  />
 )}
 {openPopup &&(
      <ConfirmationPopup
        open={openPopup}
        onClose={() => setOpenPopup(false)}
        title="Do you really want to proceed?"
        apiEndpoint="/admin/access/archive"
        onSuccess={()=>fetchArbiterInfo()}
        value={isAccess}
        Info={arbiterInfo}
      />)}
    </Container>
  );
};

export default ArbiterDetails;

/**
 * Format arbiter data for display in the detail table
 * @param {Object} arbiter - Arbiter data from API
 * @returns {Object} Formatted arbiter data with title and details
 */
function formatArbiterData(arbiter) {
  if (!arbiter || !arbiter.ArbiterDetail) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    // {
    //   label: "Full Name",
    //   value: arbiter.name || "-",
    // },
    // {
    //   label: "CBID",
    //   value: arbiter.cbid || "-",
    // },
    {
      label: "Official ID",
      value: arbiter.ArbiterDetail.officialId || "-",
    },
    {
      label: "FIDE ID",
      value: arbiter.ArbiterDetail.fideId || "-",
    },
    {
      label: "ACIF ID",
      value: arbiter.ArbiterDetail.aicfId || "-",
    },
    {
      label: "State ID",
      value: arbiter.ArbiterDetail.stateId || "-",
    },
    {
      label: "District ID",
      value: arbiter.ArbiterDetail.districtId || "-",
    },

    {
      label: "Country",
      value: arbiter.ArbiterDetail.country || "-",
    },
    {
      label: "State",
      value: arbiter.ArbiterDetail.state || "-",
    },
    {
      label: "District",
      value: arbiter.ArbiterDetail.district || "-",
    },
  ];

  return {
    title: arbiter.name || "",
    details: details,
  };
}
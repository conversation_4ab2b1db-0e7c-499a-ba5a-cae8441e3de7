import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON>po<PERSON>,
  <PERSON>rid,
  Box,
  Avatar,
  FormControlLabel,
  Checkbox,
  TextField,
  Container,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from "@mui/material";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, Controller } from "react-hook-form";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useLocation, useNavigate } from "react-router-dom";
import FormTextField from "../../components/form/FormTextField";
import FormPhoneInput from "../../components/form/FormPhoneInput";
import FormAutocomplete from "../../components/form/FormAutocomplete";
import FormSelectField from "../../components/form/FormSelectField";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import { adminPlayerProfile } from "../../schema/zod";
import ProfileImageUpload from "../../components/common/ProfileImageUpload";
import FormNumberField from "../../components/form/FormNumberField";
import BackButton from "../../components/common/BackButton";

const PlayerProfileEdit = () => {
  const location = useLocation();
  const { state } = location;
  const { userId, playerId, cbid: playerCbid } = state;

  const {
    control,
    handleSubmit,
    formState: { isValid },
    setValue,
    watch,
    getValues,
    reset,
  } = useForm({
    resolver: zodResolver(adminPlayerProfile),
    defaultValues: {
      playerTitle: "",
      firstName: "",
      lastName: "",
      email: "",
      dob: "",
      gender: "male",
      alternateContact: "",
      fideRating: "",
      aicfId: "",
      districtId: "",
      phoneNumber: "",
      club: "",
      state: "",
      city: "",
      pincode: "",
      address: "",
      parentGuardianName: "",
      emergencyContact: "",
      fideId: "",
      stateId: "",
      association: "",
      country: "",
      district: "",
      countryCode: "",
      stateCode: "",
      profileUrl: null,
    },
    reValidateMode: "onChange",
  });

  // Watch for changes to country and state codes for dependent dropdowns
  const countryCode = watch("countryCode");
  const stateCode = watch("stateCode");

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [originalProfileData, setOriginalProfileData] = useState({});
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const toast = UseToast();

  const navigate = useNavigate();

  // Load saved draft from localStorage on initial render
  useEffect(() => {
    // Check for saved draft in localStorage
    const savedDraft = localStorage.getItem("playerProfileDraft");
    if (savedDraft) {
      try {
        const draftData = JSON.parse(savedDraft);
        // Set form values from draft
        Object.keys(draftData).forEach((key) => {
          setValue(key, draftData[key]);
        });
        toast.info("Draft loaded successfully");
      } catch (error) {
        console.error("Error loading draft:", error);
      }
    }
  }, []);

  useEffect(() => {
    const fetchCountries = async () => {
      const response = await Client.get("/location/countries");
      if (!response.data.success) return;
      setCountries(response.data.data);
    };

    fetchCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    if (getValues("country")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      if (countryObj) {
        const fetchStates = async () => {
          const response = await Client.get(
            `/location/states/${countryObj.isoCode}`,
            {
              params: { country: countryObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setStates(response.data.data);

          // Only reset dependent fields if not in edit mode or if country has changed
          if (!getValues("state")) {
            setValue("state", "");
            setValue("stateCode", "");
            setValue("district", "");
            setValue("city", "");
          }
        };
        fetchStates();
      }
    } else {
      setStates([]);
      // Always reset when country is cleared
      setValue("state", "");
      setValue("stateCode", "");
      setValue("district", "");
      setValue("city", "");
    }
  }, [getValues("country"), countries, setValue]);

  useEffect(() => {
    if (getValues("country") === "India" && getValues("state")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      const stateObj = states.find((s) => s.name === getValues("state"));
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);

          // Only reset district if not in edit mode or if state has changed
          if (!getValues("district")) {
            setValue("district", "");
          }
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
      // Always reset when state is cleared
      setValue("district", "");
    }
  }, [getValues("state"), countries, states]);

  // Load cities when state changes
  useEffect(() => {
    // Get current values
    const country = getValues("country");
    const state = getValues("state");

    if (country && state) {
      const countryObj = countries.find((c) => c.name === country);
      const stateObj = states.find((s) => s.name === state);
      if (countryObj && stateObj) {
        const fetchCities = async () => {
          const response = await Client.get(
            `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setCities(response.data.data);

          // Only reset city if not in edit mode or if state has changed
          if (!getValues("city")) {
            setValue("city", "");
          }
        };
        fetchCities();
      }
    } else {
      setCities([]);
      // Always reset when state or country is cleared
      if (!state) {
        setValue("city", "");
      }
      if (!country) {
        setValue("district", "");
        setValue("city", "");
      }
    }
  }, [countries, states, getValues("state")]);

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const response = await Client.get(
          `/admin/details/single/${playerCbid}`
        );

        if (response.status === 204) {
          toast.info("You haven't created a profile yet.");
          return;
        }

        if (!response.data.success) {
          toast.info(response.data.message);
          return;
        }
        const data = response.data.data;
        const { email, phoneNumber, cbid, name } = data;
        setValue("phoneNumber", phoneNumber);
        const [firstName, lastName] = name && name.split(" ");

        setValue("firstName", firstName);
        setValue("lastName", lastName);

        const playerDetails = data?.PlayerDetail;

        // Process player details with proper club handling
        const processedDetails = {
          firstName: firstName || "",
          lastName: lastName || "",
          cbid: cbid || "",
          email: email || "",
          phoneNumber: phoneNumber || "",
          playerTitle: playerDetails.playerTitle || "",
          dob: playerDetails.dob || "",
          gender: playerDetails.gender || "male",
          alternateContact: playerDetails.alternateContact || "",
          fideRating: playerDetails.fideRating || "",
          aicfId: playerDetails.aicfId || "",
          districtId: playerDetails.districtId || "",

          // Handle club data properly
          clubId: playerDetails.clubId || "",
          club: playerDetails.club || "",

          state: playerDetails.state || "",
          city: playerDetails.city || "",
          pincode: playerDetails.pincode || "",
          address: playerDetails.address || "",
          parentGuardianName: playerDetails.parentGuardianName || "",
          emergencyContact: playerDetails.emergencyContact || "",
          fideId: playerDetails.fideId || "",
          stateId: playerDetails.stateId || "",
          association: playerDetails.association || "",
          country: playerDetails.country || "",
          district: playerDetails.district || "",
        };

        // Reset form with processed details
        reset(processedDetails);

        // Store original data for comparison
        setOriginalProfileData((prev) => ({
          ...prev,
          ...processedDetails,
        }));

        // Then find and set the country code
        if (playerDetails.country && countries.length > 0) {
          const countryObj = countries.find(
            (c) => c.name === playerDetails.country
          );
          if (countryObj) {
            setValue("countryCode", countryObj.isoCode);

            // Fetch states for this country
            const statesResponse = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              { params: { country: countryObj.isoCode } }
            );

            if (statesResponse.data.success) {
              const statesData = statesResponse.data.data;
              setStates(statesData);

              // Find and set the state code
              if (playerDetails.state) {
                const stateObj = statesData.find(
                  (s) => s.name === playerDetails.state
                );
                if (stateObj) {
                  setValue("stateCode", stateObj.isoCode);

                  // Fetch cities for this state
                  const citiesResponse = await Client.get(
                    `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
                    {
                      params: {
                        country: countryObj.isoCode,
                        state: stateObj.isoCode,
                      },
                    }
                  );

                  if (citiesResponse.data.success) {
                    setCities(citiesResponse.data.data);
                  }
                }
              }
            }
          }
        }

        // Your existing location handling code can go here...
      } catch (error) {
        console.error("Error fetching player profile:", error);
        let errorMessage = "Error fetching player data";

        if (error.response) {
          errorMessage = error.response.data?.message || "Server error";
        } else if (error.request) {
          errorMessage = "No response from server";
        } else {
          errorMessage = error.message;
        }

        toast.error("Failed to fetch player profile: " + errorMessage);
      }
    };

    fetchProfileData();
  }, [countries]);

  // Helper to scroll to error field and show error toast
  const onError = (errors) => {
    const firstField = Object.keys(errors)[0];
    const errorData = errors[firstField];

    const sectionElement = document.querySelector(`[name="${firstField}"]`);

    if (sectionElement) {
      const scrollOffset = 100;
      const y =
        sectionElement.getBoundingClientRect().top +
        window.pageYOffset -
        scrollOffset;
      window.scrollTo({ top: y, behavior: "smooth" });

      setTimeout(() => {
        if ("focus" in sectionElement) {
          sectionElement.focus();
        }
      }, 500);
    }

    toast.info(
      `Error in field: ${firstField} – ${errorData?.message || "Invalid input"}`
    );
  };

  // Form submission handler
  const onSubmit = async (data) => {
    // Save form data to localStorage as backup (excluding file fields)
    const dataForStorage = { ...data };
    delete dataForStorage.profileImage;
    delete dataForStorage.profileImagePreview;
    localStorage.setItem("playerProfileDraft", JSON.stringify(dataForStorage));

    toast.info(`Processing your profile update...`);

    try {
      // Edit mode - only send changed fields
      const changedData = {};

      // Check each field for changes
      Object.keys(data).forEach((key) => {
        // Skip these fields from comparison
        if (["profileImagePreview"].includes(key)) return;

        // Handle profile image
        if (key === "profileImage") {
          if (data.profileImage && data.profileImage instanceof File) {
            changedData.profileImage = data.profileImage;
          }
          return;
        }

        // Compare other fields with original data
        if (
          JSON.stringify(data[key]) !== JSON.stringify(originalProfileData[key])
        ) {
          changedData[key] = data[key];
        }
      });

      // Handle name changes
      if (
        data.firstName !== originalProfileData.firstName ||
        data.lastName !== originalProfileData.lastName
      ) {
        changedData.name = `${data.firstName} ${data.lastName}`;
      }

      // Check if we have any changes to send
      if (Object.keys(changedData).length === 0 && !changedData.profileImage) {
        toast.info("No changes detected to update.");
        return;
      }

      // Send update request
      await sendUpdateRequest(changedData);
      return;
    } catch (error) {
      handleError(error);
    }
  };

  // Helper function to send update request
  const sendUpdateRequest = async (changedData) => {
    const formData = new FormData();

    Object.keys(changedData).forEach((key) => {
      const value = changedData[key];

      if (key === "profileImage" && value instanceof File) {
        formData.append("profileImage", value);
      } else {
        formData.append(key, String(value));
      }
    });

    const response = await Client.put(
      `/admin/details/update/player/${userId}`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    handleResponse(response);
  };

  // Helper function to handle the API response
  const handleResponse = (response) => {
    if (!response.data.success) {
      if (response.data.zodErrors) {
        // Show the first Zod validation error
        const zodErrors = response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else {
        toast.error(response.data.error || "Unexpected error");
      }
      return;
    }

    toast.success(`Profile updated successfully`);

    // Clear draft from localStorage after successful submission
    localStorage.removeItem("playerProfileDraft");

    // Reset the form
    reset();

    // Show success dialog instead of navigating directly
    setShowSuccessDialog(true);
  };

  // Handle dialog actions
  const handleEditAgain = () => {
    setShowSuccessDialog(false);
    navigate(`/dashboard/players/${playerCbid}`);
  };

  const handleGoToDashboard = () => {
    setShowSuccessDialog(false);
    navigate('/dashboard');
  };

  // Helper function to handle errors
  const handleError = (error) => {
    console.error(`Error updating profile:`, error);

    if (error.response?.data?.error?.message === "Incorrect OTP.") {
      toast.error("Incorrect OTP. Please try again.");
    } else if (error.response?.status === 422 && error.response?.data?.data) {
      const validationErrors = error.response.data.data;
      Object.keys(validationErrors).forEach((key) => {
        if (key !== "_errors" && validationErrors[key]._errors) {
          toast.error(
            `Validation failed - ${key}: ${validationErrors[key]._errors.join(
              ", "
            )}`
          );
        }
      });
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error.message || "An error occurred");
    } else if (error.response?.data?.zodErrors) {
      // Handle Zod validation errors from the server
      const zodErrors = error.response.data.zodErrors;
      const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
      toast.error(firstZodError || "Please correct the validation errors");
    } else {
      const errorMessage =
        error.response?.data?.message ||
        `An unexpected error occurred while updating
        the profile.`;
      toast.error(errorMessage);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton to={"/dashboard"} />
      <Box maxWidth="lg" sx={{ margin: "auto", my: 2 }}>
        <Typography variant="h4" sx={{ mb: 6 }}>
          Player profile Edit
        </Typography>
        <form onSubmit={handleSubmit(onSubmit, onError)}>
          <Grid
            container
            spacing={3}
            sx={{
              ".MuiFormControl-root": { mt: "4px !important" },
              ".MuiInputBase-input": { py: 1.5 },
              ".MuiAutocomplete-input": { p: "4px !important" },
              ".MuiGrid-root.MuiGrid-item": { pt: "0px !important" },
              position: "relative",
            }}
          >
            {/* CBID and Profile Image Row */}
            <Grid container item xs={12} md={12} sx={{ mb: 2 }}>
              <Grid item xs={6} md={6}>
                <Typography variant="h6" sx={{ textAlign: "start" }}>
                  CBID: {originalProfileData.cbid || ""}
                </Typography>
                <Grid item xs={12} md={6}>
                  <FormSelectField
                    name="playerTitle"
                    control={control}
                    options={[
                      { value: "Untitled", label: "Untitled" },
                      { value: "GM", label: "Grandmaster" },
                      { value: "IM", label: "International Master" },
                      { value: "FM", label: "FIDE Master" },
                      { value: "CM", label: "Candidate Master" },
                      { value: "WGM", label: "Woman Grandmaster" },
                      { value: "WIM", label: "Woman International Master" },
                      { value: "WFM", label: "Woman FIDE Master" },
                      { value: "WCM", label: "Woman Candidate Master" },
                    ]}
                    title="Player Title"
                    placeholder="Select Player Title"
                  />
                </Grid>
              </Grid>
              <Grid item xs={6} display="flex" justifyContent="center" mb={2}>
                <ProfileImageUpload
                  control={control}
                  setValue={setValue}
                  watch={watch}
                />
              </Grid>
            </Grid>

            {/* Basic Details Fields */}
            <Grid item xs={12} md={6} sx={{ display: "flex", gap: 2 }}>
              <Grid item xs={6}>
                <FormTextField
                  name="firstName"
                  control={control}
                  title="First Name"
                  maxLength={50}
                  placeholder="Enter First Name"
                />
              </Grid>
              <Grid item xs={6}>
                <FormTextField
                  name="lastName"
                  control={control}
                  maxLength={50}
                  title="Last Name"
                  placeholder="Enter Last Name"
                />
              </Grid>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormSelectField
                name="gender"
                control={control}
                options={[
                  { value: "male", label: "Male" },
                  { value: "female", label: "Female" },
                  { value: "other", label: "Other" },
                ]}
                title="Gender"
                placeholder="Select Gender"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="dob"
                control={control}
                title="Date of Birth"
                placeholder="YYYY-MM-DD"
                type="date"
                inputProps={{
                  min: "1900-01-01",
                  max: new Date().toISOString().split("T")[0],
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <FormPhoneInput
                  name="phoneNumber"
                  control={control}
                  placeholder="Enter Mobile Number"
                  title="Phone Number"
                />
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                sx={{
                  textAlign: "start",
                  p: "0px !important",
                  m: "0px !important",
                }}
              >
                Email ID
              </Typography>

              <FormTextField
                name="email"
                control={control}
                fullWidth
                variant="outlined"
                margin="normal"
                specialCharAllowed={true}
                placeholder={"Enter email"}
                sx={{ minHeight: 70 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormNumberField
                name="fideId"
                control={control}
                title="FIDE ID"
                placeholder="Enter FIDE ID"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormNumberField
                name="fideRating"
                control={control}
                title="FIDE Rating"
                placeholder="Enter FIDE Rating"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="aicfId"
                control={control}
                maxLength={20}
                title="AICF ID"
                placeholder="Enter AICF ID"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="stateId"
                control={control}
                title="State ID"
                maxLength={20}
                placeholder="Enter State ID"
                specialCharAllowed={true}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormTextField
                name="districtId"
                control={control}
                title="District ID"
                maxLength={20}
                placeholder="Enter District ID"
                specialCharAllowed={true}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="club"
                control={control}
                title="Club"
                placeholder="Enter Club"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormPhoneInput
                name="alternateContact"
                control={control}
                title="Alternate Contact"
                placeholder="Enter Alternate Contact"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="parentGuardianName"
                control={control}
                title="Parent/Guardian Name"
                maxLength={100}
                placeholder="Enter Parent/Guardian Name"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormPhoneInput
                name="emergencyContact"
                control={control}
                title="Parent/Guardian Contact"
                placeholder="Enter Parent/Guardian Contact"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="association"
                control={control}
                maxLength={100}
                title="Association"
                placeholder="Enter Association"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="country"
                control={control}
                options={countries}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                getOptionLabel={(option) => option.name}
                title="Country"
                placeholder="Select Country"
                maxLength={50}
                onChange={(_, newValue) => {
                  if (newValue) {
                    setValue("country", newValue.name);
                    setValue("countryCode", newValue.isoCode);
                    setValue("state", "");
                    setValue("stateCode", "");
                    setValue("city", "");
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="state"
                control={control}
                options={states}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                getOptionLabel={(option) => option.name}
                title="State"
                placeholder="Select State"
                disabled={!countryCode}
                onChange={(_, newValue) => {
                  if (newValue) {
                    setValue("state", newValue.name);
                    setValue("stateCode", newValue.isoCode);
                    setValue("city", "");
                  }
                }}
              />
            </Grid>

            {getValues("country") === "India" && (
              <Grid item xs={12} md={6}>
                <FormAutocomplete
                  name="district"
                  control={control}
                  sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                  title="District"
                  placeholder="Select District"
                  options={districts}
                  getOptionLabel={(option) => option.name}
                  disabled={!stateCode || !countryCode}
                  onChange={(_, newValue) => {
                    if (newValue) {
                      setValue("district", newValue.name);
                    }
                  }}
                />
              </Grid>
            )}
            {(getValues("country") !== "India" ||
              getValues("country") === "") && (
              <Grid item xs={12} md={6}>
                <FormTextField
                  name="district"
                  control={control}
                  title="District"
                  placeholder="Enter District"
                />
              </Grid>
            )}

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="city"
                control={control}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                options={cities}
                getOptionLabel={(option) => option.name}
                title="City"
                placeholder="Select City"
                disabled={!stateCode || !countryCode}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormNumberField
                name="pincode"
                control={control}
                title="Pincode"
                placeholder="Enter Pincode"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  maxLength: 6,
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="address"
                control={control}
                title="Address"
                placeholder="Enter Address"
                maxLength={150}
                multiline
                rows={3}
                specialCharAllowed={true}
              />
            </Grid>

            {/* Submit Buttons */}
            <Grid item xs={12}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  gap: 2,
                  mt: 6,
                }}
              >
                <Button
                  type="submit"
                  variant="contained"
                  sx={{
                    fontSize: 16,
                    px: 2,
                    bgcolor: isValid
                      ? "hsla(120, 49%, 35%, 1)"
                      : "hsla(0, 3%, 80%, 1)",
                    "&:hover": {
                      bgcolor: isValid
                        ? "rgb(39, 104, 39)"
                        : "hsla(0, 3%, 80%, 1)",
                    },
                  }}
                  size="large"
                >
                  Update
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Box>

      {/* Success Dialog */}
      <Dialog
        open={showSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ textAlign: "center", fontWeight: "bold", color: "#4caf50" }}>
          Profile Updated Successfully!
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ textAlign: "center", fontSize: "16px", color: "#333" }}>
            The player profile has been updated successfully. What would you like to do next?
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", gap: 2, pb: 3 }}>
          <Button
            onClick={handleEditAgain}
            variant="outlined"
            color="primary"
            sx={{
              px: 3,
              py: 1,
              fontSize: "16px",
              textTransform: "none",
              borderRadius: 2,
            }}
          >
            Edit Again
          </Button>
          <Button
            onClick={handleGoToDashboard}
            variant="contained"
            color="primary"
            sx={{
              px: 3,
              py: 1,
              fontSize: "16px",
              textTransform: "none",
              borderRadius: 2,
              bgcolor: "#4caf50",
              "&:hover": {
                bgcolor: "#45a049",
              },
            }}
          >
            Go to Dashboard
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PlayerProfileEdit;

import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Divider,
  Alert,
  Link,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableRow
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ReceiptIcon from '@mui/icons-material/Receipt';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import DownloadIcon from '@mui/icons-material/Download';
import UseToast from '../lib/hooks/UseToast';
import { AxiosError } from 'axios';

const PaymentSuccessPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();
  const [paymentInfo, setPaymentInfo] = useState({
    txnId: '',
    amount: '',
    tournamentName: '',
    date: new Date().toLocaleDateString()
  });

  useEffect(() => {
    // Hard coded
    // Parse URL parameters
    const search = location.search;

    // Extract transaction ID if available
    const txnidMatch = search.match(/txnid=([^&?]+)/);
    const txnid = txnidMatch ? txnidMatch[1] : '';

    // Extract amount if available
    const amountMatch = search.match(/amount=([^&?]+)/);
    const amount = amountMatch ? decodeURIComponent(amountMatch[1]) : '';

    // Extract tournament name if available
    const tournamentMatch = search.match(/tournament=([^&?]+)/);
    const tournament = tournamentMatch ? decodeURIComponent(tournamentMatch[1]) : '';

    setPaymentInfo({
      txnId: txnid,
      amount: amount,
      tournamentName: tournament,
      date: new Date().toLocaleDateString()
    });
  }, [location.search]);
  
  const HandleReceipt = async (txnId) => {
    try {
      const { generatePdfAndDownload } = await import(
        "../components/common/PaymentReceiptTemplate"
      );

      await generatePdfAndDownload({ toast, txnId });
    } catch (error) {
      if (error instanceof AxiosError && error.response) {
        toast.info(error.response.data.error);
        return;
      }
      console.error("Receipt generation failed:", error);
      toast.error("Failed to generate receipt PDF");
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          borderRadius: 2,
          border: '1px solid #e0e0e0',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <CheckCircleOutlineIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
          <Typography variant="h4" component="h1" color="success.main">
            Registration Successful
          </Typography>
        </Box>

        <Alert severity="success" sx={{ mb: 3, fontSize: 16 }}>
          Thank you! Your payment has been successfully processed and your tournament registration is confirmed.
        </Alert>

        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Payment Details
          </Typography>

          <TableContainer component={Paper} sx={{ bgcolor: 'grey.50' }}>
            <Table >
              <TableBody>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Transaction ID</TableCell>
                  <TableCell>{paymentInfo.txnId || 'N/A'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Date</TableCell>
                  <TableCell>{paymentInfo.date || 'N/A'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Amount Paid</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>₹ {paymentInfo.amount || 'N/A'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Tournament</TableCell>
                  <TableCell>{paymentInfo.tournamentName || 'N/A'}</TableCell>
                </TableRow>
              </TableBody>
            </Table> 
          </TableContainer>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Typography variant="h6" gutterBottom>
          What's Next?
        </Typography>

        <Box component="ul" sx={{ pl: 2 }}>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            Your registration for the tournament is confirmed
          </Typography>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            A confirmation email has been sent to your registered email address
          </Typography>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            You can view tournament details and your registration in your dashboard
          </Typography>
          <Typography component="li" variant="body1">
            If you have any questions, feel free to contact our support team
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mt: 4 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard')}
            sx={{ flex: 1, fontSize: 16 }}
          >
            Go to Dashboard
          </Button>

          <Button
            variant="contained"
            color="primary"
            startIcon={<EmojiEventsIcon />}
            onClick={() => navigate('/tournaments')}
            sx={{
              flex: 1,
              fontSize: 16,
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              }
            }}
          >
            View Tournaments
          </Button>
        </Box>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<DownloadIcon />}
            onClick={() =>HandleReceipt(paymentInfo.txnId)}
            sx={{ fontSize: 16 }}
          >
            Download Receipt
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default PaymentSuccessPage;
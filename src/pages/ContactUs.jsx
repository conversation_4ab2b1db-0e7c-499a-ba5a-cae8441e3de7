import {
  <PERSON>,
  Button,
  Stack,
  <PERSON><PERSON>ield,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React from "react";
import image from "../assets/image.png";
import { Phone, Email, LocationOn } from "@mui/icons-material";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Client } from "../api/client";
import UseToast from "../lib/hooks/UseToast";

const contactSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(50, "Name must be less than 50 characters"),

  email: z.string().min(1, "Email is required").email("Invalid email address"),

  phone: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .max(15, "Phone number must not exceed 15 digits")
    .regex(/^[0-9]+$/, "Phone number must contain only digits"),

  message: z
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(1000, "Message must be less than 1000 characters"),
  subject: z
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(1000, "Message must be less than 1000 characters"),
});

const ContactUs = () => {
  const toast = UseToast();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(contactSchema),
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const formFields = [
    {
      name: "name",
      label: "Your Name",
      type: "text",
      placeholder: "Your Name",
      required: true,
    },
    {
      name: "email",
      label: "Email",
      type: "email",
      placeholder: "Your Email",
      required: true,
    },
    {
      name: "phone",
      label: "Phone Number",
      type: "tel",
      placeholder: "Your Phone Number",
      required: false,
    },
    {
      name: "subject",
      label: "Subject",
      type: "text",
      placeholder: "Enter your question",
      required: true,
    },
    {
      name: "message",
      label: "Your Message",
      type: "textarea",
      placeholder: "Type Your Message here...",
      required: true,
    },
  ];
  const onsubmit = async (data) => {
    const response = await Client.post("/auth/contact-us", data);
    if (response.data.success) {
      toast.success(response.data.data.message);
      setTimeout(() => {
        reset();
      }, 1000);
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        alignItems: "center",
        justifyContent: "center",
        display: "flex",
        bgcolor: "white",
        width: "100%",
        padding: { xs: "20px", sm: "40px", md: "60px", lg: "80px" },
      }}
    >
      <Box
        sx={{
          backgroundPosition: "center",
          alignItems: "center",
          justifyContent: "center",
          display: "flex",
          width: "100%",
          height: "100%",
          position: "relative",
          background: "#000",
          borderRadius: "15px",
        }}
      >
        <Box
          component="img"
          src={image}
          alt="logo"
          sx={{
            position: "absolute",
            width: { xs: "100%", sm: "100%", md: "100%", lg: "600px" },
            height: "100%",
            left: { sm: "0px", md: "0px", lg: "110px" },
            zIndex: 0,
            objectFit: "cover",
          }}
        />

        <Box
          sx={{
            position: "relative",
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "column",
              md: "column",
              lg: "row",
            },
            alignItems: "start",
            justifyContent: "space-evenly",
            padding: { xs: "20px", sm: "40px" },
            width: "100%",
            zIndex: 4,
            gap: "50px",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              justifyContent: "flex-start",
              gap: "30px",
            }}
          >
            <Typography variant="h3" sx={{ color: "white" }}>
              Contact Us
            </Typography>

            <Stack
              direction={"column"}
              spacing={{ xs: 1.5, sm: 2, md: 4, lg: 4 }}
            >
              <a
                href={`tel:${9840405141}`}
                style={{ textDecoration: "none", color: "#000000 !important" }}
              >
                <Typography
                  sx={{ color: "white", fontSize: {xs:16,md:18,lg:20} }}
                >
                  <Phone
                    sx={{
                      verticalAlign: "middle",
                      marginRight: "8px",
                      fontSize: { xs: "20px", md: "25px", lg: "30px" },
                    }}
                  />
                  +91-98404 05141
                </Typography>
              </a>

              <Typography
                component={"a"}
                href="mailto:<EMAIL>"
                sx={{ color: "white", fontSize: {xs:16,md:18,lg:20}}}
              >
                <Email
                  sx={{
                    verticalAlign: "middle",
                    marginRight: "8px",
                    fontSize:  { xs: "20px", md: "25px", lg: "30px" },
                  }}
                />
                <EMAIL>
              </Typography>
              <Typography
                sx={{ color: "white", fontSize:{xs:16,md:18,lg:20} }}
              >
                <LocationOn
                  sx={{
                    verticalAlign: "middle",
                    marginRight: "8px",
                    fontSize:  { xs: "20px", md: "25px", lg: "30px" },
                  }}
                />
                Plot No: 55, Flat GA, Sahaj Terrace,
                <br />
                <span style={{ marginLeft: "30px" }} />
                Gowri Nagar 1st Street, Mugalivakkam,
                <br /> <span style={{ marginLeft: "30px" }} />
                Chennai - 600125
              </Typography>
            </Stack>
          </Box>
          <Box
            sx={{
              width: { sm: "80%", md: "80%", lg: "30%" },
              //  bgcolor:{xs:'transparent',sm:'transparent',md:'transparent',lg:'white'},
              bgcolor: { sm: "transparent", md: "transparent", lg: "white" },
              padding: { md: "40px", lg: "40px" },
              borderRadius: "15px",
              alignSelf: "center",
            }}
          >
            <form onSubmit={handleSubmit(onsubmit)}>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {formFields.map((field) => (
                  <Box
                    key={field.name}
                    sx={{
                      marginBottom: "20px",
                      minWidth: {
                        xs: "100%",
                        sm: "100%",
                        md: "100%",
                        lg: "300px",
                      },
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      sx={{
                        color: {
                          xs: "white",
                          sm: "white",
                          md: "white",
                          lg: "black",
                        },
                        marginBottom: "5px",
                        fontSize: isMobile ? "10px" : "16px",
                      }}
                    >
                      {field.label} *
                    </Typography>
                    {field.type === "textarea" ? (
                      <TextField
                        name={field.name}
                        multiline
                        rows={4}
                        {...register(field.name)}
                        variant="outlined"
                        sx={{
                          bgcolor: "white",
                          borderRadius: "8px",
                          fontSize: isMobile ? "10px" : "16px",
                        }}
                        fullWidth
                        placeholder={field.placeholder}
                        required
                        error={Boolean(errors[field.name])}
                        helperText={errors[field.name]?.message}
                      />
                    ) : (
                      <TextField
                        name={field.name}
                        type={field.type}
                        variant="outlined"
                        size="small"
                        {...register(field.name)}
                        sx={{
                          bgcolor: "white",
                          borderRadius: "8px",
                          fontSize: isMobile ? "10px" : "16px",
                        }}
                        fullWidth
                        placeholder={field.placeholder}
                        required
                        error={Boolean(errors[field.name])}
                        helperText={errors[field.name]?.message}
                      />
                    )}
                  </Box>
                ))}
                <Button
                  type="submit"
                  size="small"
                  variant="contained"
                  sx={{ marginTop: "10px", background: "#29913E" }}
                >
                  Submit
                </Button>
              </Box>
            </form>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ContactUs;

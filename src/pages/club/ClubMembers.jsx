import React, { useState, useCallback, lazy } from "react";
import { Box, Button, Container, Paper } from "@mui/material";
import { Client } from "../../api/client";

import { Link } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import ContactDetailsButton from "../../components/common/ContactDetailsButtton";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const PlayersSearchForm = lazy(() =>
  import("../../components/common/PlayersSearchForm")
);

const ClubMembers = () => {
  // States for form inputs
  const [search, setSearch] = useState({
    playerName: "",
    playerId: "",
    country: "",
    state: "",
    district: "",
    city: "",
    mobile: "",
    email: "",
  });
  const { currentProfile } = UseGlobalContext();

  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [players, setPlayers] = useState([]);

  const toast = UseToast();

  // Define fetchPlayers function with useCallback
  const fetchPlayers = useCallback(async () => {
    setLoading(true);

    try {
      const response = await Client.get("/club/members", {
        params: { ...search, page, limit },
      });
      if (response.status === 204) {
        setPlayers([]);
        toast.info("No players found");
        return;
      }
      const { players, currentPage: cp, totalPages: tp } = response.data.data;
      setTotalPages(tp || 1);
      setPage(cp || 1);
      setPlayers(players);
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 422) {
        console.error("Validation error:", error.response.data);
        toast.info("Please correct the search criteria");
      } else {
        console.error("Error fetching players:", error);
        toast.error("Failed to fetch players. Please try again.");
      }
      setPlayers([]);
    } finally {
      setLoading(false);
    }
  }, [search, page, limit]);

  const fetchClubPlayersReport = async (type) => {
    if (!currentProfile) return;
    // return;
    // setLoading(true);
    try {
      const response = await Client.get(
        `/report/club-players/${currentProfile.id}`,
        {
          params: { type: type },
          responseType: "blob",
        }
      );

      
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = window.URL.createObjectURL(blob);
      
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "club_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  // Handle search button click
  const handleSearch = () => {
    setPage(1); // Reset to first page on new search
    fetchPlayers(1);
  };
  const handleReset = () => {
    setSearch({
      name: "",
      cbid: "",
      country: "",
      state: "",
      district: "",
      city: "",
      mobile: "",
      email: "",
    });
  };

  // Handle page change
  const handlePageChange = (_, newPage) => {
    setPage(newPage);
    fetchPlayers(newPage);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, pb: 8, minHeight: "100vh" }}>
      <BackButton />
      <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
        <Button
          size="small"
          variant="contained"
          disabled={players?.length === 0}
          onClick={() => fetchClubPlayersReport("xlsx")}
        >
          {" "}
          Download Report
        </Button>
      </Box>
      {/* Search Form */}
      <PlayersSearchForm
        search={search}
        setSearch={setSearch}
        handleSearch={handleSearch}
        loading={loading}
        handleReset={handleReset}
        mobileEmail={true}
      />

      {/* Club Members Table */}
      <Paper>
        <DynamicTable
          columns={[
            { id: "playerTitle", label: "Title", width: "80px" },
            { id: "name", label: "Player Name" },
            { id: "fideRating", label: "FIDE Rating" },
            { id: "fideId", label: "FIDE ID" },
            { id: "cbid", label: "CBID" },
            { id: "aicfId", label: "National ID" },
            { id: "stateId", label: "State ID" },
            { id: "districtId", label: "District ID" },
            {
              id: "contact",
              label: "Contact",
              align: "right",
              format: (_, player) => (
                <ContactDetailsButton
                  data={player}
                  size="small"
                  variant="contained"
                  color="primary"
                  sx={{
                    bgcolor: "#4caf50",
                    "&:hover": {
                      bgcolor: "#388e3c",
                    },
                  }}
                />
              ),
            },
          ]}
          data={players}
          loading={loading}
          page={page}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          detailsPath="/players/"
          idField="cbid"
          skeletonRows={3}
          tableContainerProps={{
            sx: {
              minHeight: "400px",
              maxHeight: "600px",
            },
          }}
        />

        <Box sx={{ width: "100%", textAlign: "center", p: 2 }}>
          <Button
            variant="contained"
            size="small"
            color="secondary"
            sx={{ mt: 2 }}
            component={Link}
            to="/players"
          >
            Add New Member
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default ClubMembers;

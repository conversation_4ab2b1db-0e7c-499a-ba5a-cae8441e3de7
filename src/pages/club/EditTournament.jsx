import React, { useEffect, useState } from "react";
// Note: Grid is marked as deprecated in MUI but is still the recommended approach until the new system is stable
import {
  Box,
  Container,
  Paper,
  Stack,
  Button,
  Typography,
  CircularProgress,
  Grid,
} from "@mui/material";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { tournamentEditSchema } from "../../schema/tournament";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

// Import form components

import FormVenueDetails from "../../components/form/FormVenueDetails";
import FormOtherDetails from "../../components/form/FormOtherDetails";
import FormTournamentDetails from "../../components/form/FormTournamentDetails";
import FormContactDetails from "../../components/form/FormContactDetails";
import FormPrizeDetails from "../../components/form/FormPrizeDetails";
import { useNavigate, useParams } from "react-router-dom";
import BackButton from "../../components/common/BackButton";

const EditTournamentPage = () => {
  const toast = UseToast();
  const [loading, setLoading] = React.useState(false);
  const [submitting, setSubmitting] = React.useState(false);
  const edit = true;
  const [originalValue, setOriginalValue] = useState({});
  const navigate = useNavigate();

  // Initialize React Hook Form
  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    reset,
    formState: { errors, isValid },
  } = useForm({
    resolver: zodResolver(tournamentEditSchema),
    defaultValues: {
      title: "",
      organizerName: "",
      fideRated:"false",
      tournamentLevel: "state",
      startDate: "",
      endDate: "",
      reportingTime: "09:00 AM",
      maleAgeCategory: "",
      femaleAgeCategory: "",
      registrationStartDate: "",
      registrationEndDate: "",
      registrationEndTime: "09:00 AM",
      tournamentDirectorName: "",
      entryFeeCurrency: "INR",
      entryFee: null,
      chatUrl: null,
      timeControl: "classical",
      timeControlDuration: "",
      timeControlIncrement: "",
      tournamentType: "individual",
      tournamentSystem: "swiss-system",
      nationalApproval: "",
      stateApproval: "",
      districtApproval: "",
      contactPersonName: "",
      email: "",
      contactNumber: "",
      alternateContact: "",
      numberOfTrophiesFemale: null,
      numberOfTrophiesMale: null,
      totalCashPrizeCurrency: "",
      totalCashPrizeAmount: null,
      country: "",
      state: "",
      district: "",
      city: "",
      pincode: "",
      venueAddress: "",
      nearestLandmark: "",
      locationUrl: "",
      chessboardProvided: false,
      spotEntry: false,
      timerProvided: false,
      parkingFacility: "no",
      foodFacility: ["nil"],
      subTitle: "",
      presentedBy: "",
    },
    mode: "onSubmit",
    reValidateMode: "onChange",
  });

  // Get tournament ID from URL
  const { title: tournamentId } = useParams();

  useEffect(() => {
    const fetchTournament = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${tournamentId}`);
        if (response.data.success) {
          const tournamentData = response.data.data;
          const {title,...rest} = tournamentData;

          const newTitle = title.replace(/-/g, " ");
         
          // Set all form values at once
          reset({title:newTitle,...rest});

          // Properly set original value
          setOriginalValue({title:newTitle,...rest});

          toast.success("Tournament details loaded successfully");
        } else {
          toast.error("Failed to fetch tournament details");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error("Error fetching tournament details");
      } finally {
        setLoading(false);
      }
    };

    fetchTournament();
  }, [tournamentId, setValue]);
  // Form submission handler
  const onSubmit = async (data) => {
    setSubmitting(true);
    try {
      const changedData = {};
      // Check each field and only include ones that have changed from original
      Object.keys(data).forEach((key) => {
        // Skip preview fields from comparison
        if (["brochureFilePreview"].includes(key)) return; 
        // Special handling for file fields
        if (key === "brochure") {
          if (data.brochure && data.brochure instanceof File) {
            changedData.brochure = data.brochure;
          }
          return;
        }

        // Compare with our stored original data
        if (JSON.stringify(data[key]) !== JSON.stringify(originalValue[key])) {
          changedData[key] = data[key];
        }
      });

      // Only proceed with the API call if we have changes
      if (Object.keys(changedData).length === 0 && !changedData.brochure) {
        toast.info("No changes detected to update.");
        return;
      }
      // Create FormData to handle file upload
      const formData = new FormData();

      // Add all changed fields to FormData with special handling for known complex fields
      Object.keys(changedData).forEach((key) => {
        const value = changedData[key];

        // Handle file upload
        if (key === "brochure" && value instanceof File) {
          formData.append("brochure", value);
          return;
        }

        // Special handling for fields we know should be arrays based on schema
        if (
          ["maleAgeCategory", "femaleAgeCategory", "foodFacility"].includes(key)
        ) {
          // Ensure these are always sent as JSON arrays
          // Avoid sending empty array/object if it's not changedconst arrayValue = Array.isArray(value) ? value : [value].filter(Boolean);
          if (!value || value.length === 0) return;
          formData.append(key, JSON.stringify(value));
          return;
        }
        // if (key === "fideRated") {
        //   formData.append(key, value === "true" ? true : false);
        //   return;
        // }


        // General handling for other objects and arrays
        if (typeof value === "object" && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          // Simple values (strings, numbers, booleans)
          formData.append(key, value);
        }
      });

      // Log what's being sent (but not the actual file contents)
      const response = await Client.put(
        `/tournament/${originalValue.id}`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      handleResponse(response);
      return;
    } catch (error) {
      handleError(error);
    } finally {
      setSubmitting(false);
    }
  };

const onError = (errors) => {
  const firstField = Object.keys(errors)[0];
  const errorData = errors[firstField];

  const sectionElement = document.querySelector(`[name="${firstField}"]`);

  if (sectionElement) {
    const scrollOffset = 100;
    const y =
      sectionElement.getBoundingClientRect().top +
      window.pageYOffset -
      scrollOffset;
    window.scrollTo({ top: y, behavior: "smooth" });

    setTimeout(() => {
      if ("focus" in sectionElement) {
        sectionElement.focus();
      }
    }, 500);
  }

  toast.info(
    `Error in field: ${firstField} – ${errorData?.message || "Invalid input"}`
  );
};


  const handleResponse = (response) => {
    if (!response.data.success) {
      if (response.data.zodErrors) {
        // Show the first Zod validation error
        const zodErrors = response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else {
        toast.error(response.data.error || "Unexpected error");
      }
      return;
    }

    toast.success(`Tournament ${edit ? "updated" : "created"} successfully`);

    // Clear draft from localStorage after successful submission
    localStorage.removeItem("tournamentDraft");

    // Reset the form
    reset();

    // Navigate back to profile page
    navigate("/dashboard/tournaments");
  };

  // Helper function to handle errors
  const handleError = (error) => {
    console.error(
      `Error ${edit ? "updating" : "creating"} Tournaments:`,
      error
    );

    if (error.response?.status === 422 && error.response?.data?.data) {
      const validationErrors = error.response.data.data;
      Object.keys(validationErrors).forEach((key) => {
        if (key !== "_errors" && validationErrors[key]._errors) {
          toast.error(
            `Validation failed - ${key}: ${validationErrors[key]._errors.join(
              ", "
            )}`
          );
        }
      });
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error.message || "An error occurred");
    } else if (error.response?.data?.zodErrors) {
      // Handle Zod validation errors from the server
      const zodErrors = error.response.data.zodErrors;
      const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
      toast.error(firstZodError || "Please correct the validation errors");
    } else {
      const errorMessage =
        error.response?.data?.message ||
        `An unexpected error occurred while ${
          edit ? "updating" : "creating"
        } the tournament.`;
      toast.error(errorMessage);
    }
  };

  // Show loading indicator while fetching tournament data
  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading tournament data...
        </Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 8, px: "4vw" }}>
      <BackButton to="/dashboard/tournaments" />
      <Box
        sx={{
          bgcolor: "white",
          minHeight: "100vh",
        }}
      >
        <Paper
          elevation={3}
          sx={{
            borderRadius: "30px",
            p: 4,
            "& .MuiTextField-root": {
              mt: 0.5,
              mb: 0.5,
            },
          }}
        >
          <form
            onSubmit={handleSubmit(onSubmit, onError)}
          >
            {/* Tournament Details Section */}
            <FormTournamentDetails
              control={control}
              setValue={setValue}
              watch={watch}
              edit={edit}
            />

            {/* Contact Details Section */}
            <FormContactDetails
              control={control}
              setValue={setValue}
              watch={watch}
            />

            {/* Prize Details Section */}
            <FormPrizeDetails
              control={control}
              setValue={setValue}
              watch={watch}
            />

            <FormVenueDetails
              control={control}
              setValue={setValue}
              watch={watch}
              getValues={getValues}
              errors={errors}
            />

            {/* Other Details Section */}
            <FormOtherDetails control={control} />

            {/* Submit Buttons */}
            <Stack
              direction="row"
              spacing={2}
              justifyContent="center"
              sx={{ mt: 8, mb: 4 }}
            >
              <Button
                variant="contained"
                size="small"
                type="submit"
                disabled={submitting}
                sx={{
                  borderRadius: "10px",
                  bgcolor: isValid ? "#2c2891" : "#666",
                  fontSize: "20px",
                  "&:hover": {
                    bgcolor: isValid ? "#1a1b60" : "#555",
                  },
                }}
              >
                {submitting ? "Updating..." : "Update Tournament"}
              </Button>
            </Stack>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default EditTournamentPage;

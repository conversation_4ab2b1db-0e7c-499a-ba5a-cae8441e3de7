import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Card,
  CardContent,
  Autocomplete,
  CircularProgress,
  MenuItem,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import PaymentIcon from "@mui/icons-material/Payment";
import PersonIcon from "@mui/icons-material/Person";
import EventIcon from "@mui/icons-material/Event";
import RefundIcon from "@mui/icons-material/MoneyOff";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import { RestartAlt } from "@mui/icons-material";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const TournamentEarningsPage = () => {
  const [clubPayments, setClubPayments] = useState([]);
  const [playerPayments, setPlayerPayments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tournamentLoading, setTournamentLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [platformFeePercent, setPlatformFeePercent] = useState(0);

  const [shouldFetch, setShouldFetch] = useState(false);
  const [tournaments, setTournaments] = useState([]);
  const [selectedTournament, setSelectedTournament] = useState(null);
  const [summaryStats, setSummaryStats] = useState({
    totalPayers: 0,
    totalAmount: 0,
    refundAmount: 0,
    refundedPayers: 0,
    platformFeeAmount: 0,
    tournamentPayout: 0,
    currency: "INR",
    successfulPayments: 0,
    refundedPayments: 0,
  });

  // States for form inputs
  const [search, setSearch] = useState({
    transactionId: "",
    tournamentTitle: "",
    paymentType: "all",
  });

  // Separate filters for Club Payments
  const [clubFilters, setClubFilters] = useState({
    tournamentTitle: "",
    paymentStatus: "all",
    startDate: "",
    endDate: "",
  });

  // Separate filters for Player Payments
  const [playerFilters, setPlayerFilters] = useState({
    tournamentTitle: "",
    paymentStatus: "all",
    startDate: "",
    endDate: "",
  });
  const [page, setPage] = useState(1);
  const limit = 10;
  const toast = UseToast();

  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayments(value);
  };

  // Handle tournament selection
  const handleTournamentChange = (_, newValue) => {
    setSelectedTournament(newValue);
    if (newValue && newValue.title) {
      setSearch((prev) => ({ ...prev, tournamentTitle: newValue.title }));
      fetchPayments(1);
    } else {
      setSearch((prev) => ({ ...prev, tournamentTitle: "" }));
    }
  };

  const handleReset = () => {
    // Reset main search form
    setSearch({
      transactionId: "",
      tournamentTitle: "",
      paymentType: "all",
    });

    // Reset Club Payments table filters
    setClubFilters({
      tournamentTitle: "",
      paymentStatus: "all",
      startDate: "",
      endDate: "",
    });

    // Reset Player Payments table filters
    setPlayerFilters({
      tournamentTitle: "",
      paymentStatus: "all",
      startDate: "",
      endDate: "",
    });

    // Reset tournament selection
    setSelectedTournament(null);

    // Fetch fresh data
    setShouldFetch(true);

    // Show user feedback
    toast.info("All filters reset successfully");
  };

  const handleSearch = () => {
    setShouldFetch(true);
  };

  // Fetch platform fee
  const fetchPlatformFee = useCallback(async () => {
    try {
      const response = await Client.get("/tournament/platform-fee");
      if (response.data.success) {
        const platformFeePercentage = parseFloat(response.data.data.fee);
        setPlatformFeePercent(platformFeePercentage);
      }
    } catch (error) {
      console.error("Error fetching platform fee:", error);
    }
  }, []);

  // Define fetchPayments with enhanced refund and payout calculations
  const fetchPayments = useCallback(
    async (pageNumber) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };
        // Add tournament filter from dropdown selection
        if (selectedTournament && selectedTournament.title) {
          params.tournamentTitle = selectedTournament.title;
        } else if (search.tournamentTitle !== "") {
          params.tournamentTitle = search.tournamentTitle;
        }
        if (search.transactionId !== "") {
          params.transactionId = search.transactionId;
        }
        if (search.paymentType !== "all") {
          params.paymentType = search.paymentType;
        }
      

        const response = await Client.get("/payment/club/tournament", {
          params,
        });

        if (response.status === 204) {
          toast.info("No payment records found");
          setClubPayments([]);
          setPlayerPayments([]);
          setTotalPages(0);
          setPage(1);
          setSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            refundAmount: 0,
            refundedPayers: 0,
            platformFeeAmount: 0,
            tournamentPayout: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
            refundedPayments: 0,
          });
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        const paymentsData = response.data.data.payments;

        // Split payments by type
        const clubPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "club"
        );
        const playerPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "player"
        );

        setClubPayments(clubPaymentsData);
        setPlayerPayments(playerPaymentsData);

        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);

        // Enhanced statistics calculation
        const stats = {
          totalPayers: 0,
          totalAmount: 0,
          refundAmount: 0,
          refundedPayers: 0,
          platformFeeAmount: 0,
          tournamentPayout: 0,
          currency: "INR",
          successfulPayments: 0,
          pendingPayments: 0,
          failedPayments: 0,
          refundedPayments: 0,
        };

        // Calculate stats from payments data
        paymentsData.forEach((payment) => {
          const status = payment.paymentStatus?.toLowerCase();
          const amount = parseFloat(payment.paymentAmount || 0);

          // Set currency if available
          if (payment.paymentCurrency) {
            stats.currency = payment.paymentCurrency;
          }

          // Count players
          if (payment.paymentType === "club") {
            if(status === "captured"){
              stats.totalPayers += payment.playersCount || 0;
            }
          } else if (payment.paymentType === "player") {
            if(status === "captured"){
              stats.totalPayers += 1;
            }
          }

          // Calculate amounts based on status
          if (
            status === "captured" ||
            status === "paid" ||
            status === "success"
          ) {
            stats.totalAmount += amount;
            stats.successfulPayments++;
   
          } else if (status === "refunded") {
            stats.refundAmount += amount;
            stats.refundedPayments++;

            // Count refunded players
            if (payment.paymentType === "club") {
              stats.refundedPayers += payment.playersCount || 0;
            } else if (payment.paymentType === "player") {
              stats.refundedPayers += 1;
            }
          } else if (status === "pending") {
            stats.pendingPayments++;
          } else if (status === "failed") {
            stats.failedPayments++;
          }
        });

        // Calculate platform fee and tournament payout
       
        stats.platformFeeAmount = (stats.totalAmount * platformFeePercent) / 100;
        stats.tournamentPayout = stats.totalAmount - stats.platformFeeAmount;

        setSummaryStats(stats);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [limit, search, platformFeePercent]
  );

  // Define fetchTournaments with useCallback
  const fetchTournaments = useCallback(async () => {
    setTournamentLoading(true);
    try {
      const response = await Client.get("/tournament/club", {
        params: { limit: 100, page: 1 },
      });

      if (response.status === 204 || !response.data.success) {
        setTournaments([]);
        return;
      }

      setTournaments(response.data.data.tournaments || []);
    } catch (error) {
      console.error("Error fetching tournaments:", error);
      toast.error("Failed to load tournaments");
    } finally {
      setTournamentLoading(false);
    }
  }, []);

  useEffect(() => {
    if (shouldFetch) {
      fetchPayments(1);
      setShouldFetch(false);
    }
  }, [shouldFetch, fetchPayments]);

  // Fetch tournaments and platform fee on component mount
  useEffect(() => {
    fetchTournaments();
    fetchPlatformFee();
  }, [fetchTournaments, fetchPlatformFee]);

  // Format date for display (date only)
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  // Format time for display (time only)
  const formatTime = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleTimeString("en-US", options);
  };

  // Fetch payments with filters
  const fetchPaymentsWithFilters = useCallback(
    async (pageNumber, additionalFilters = {}) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };

        // Add main search filters
        // Add tournament filter from dropdown selection
        if (selectedTournament && selectedTournament.title) {
          params.tournamentTitle = selectedTournament.title;
        } else if (search.tournamentTitle !== "") {
          params.tournamentTitle = search.tournamentTitle;
        }
        if (search.transactionId !== "") {
          params.transactionId = search.transactionId;
        }
        if (search.paymentType !== "all") {
          params.paymentType = search.paymentType;
        }

        // Add additional filters (club or player specific)
        Object.assign(params, additionalFilters);

        const response = await Client.get("/payment/club/tournament", {
          params,
        });

        if (response.status === 204) {
          toast.info("No payment records found");
          setClubPayments([]);
          setPlayerPayments([]);
          setTotalPages(0);
          setPage(1);
          setSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            refundAmount: 0,
            refundedPayers: 0,
            platformFeeAmount: 0,
            tournamentPayout: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
            refundedPayments: 0,
          });
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        const paymentsData = response.data.data.payments;

        // Split payments by type
        const clubPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "club"
        );
        const playerPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "player"
        );

        setClubPayments(clubPaymentsData);
        setPlayerPayments(playerPaymentsData);

        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);

        // Enhanced statistics calculation
        const stats = {
          totalPayers: 0,
          totalAmount: 0,
          refundAmount: 0,
          refundedPayers: 0,
          platformFeeAmount: 0,
          tournamentPayout: 0,
          currency: "INR",
          successfulPayments: 0,
          pendingPayments: 0,
          failedPayments: 0,
          refundedPayments: 0,
        };

        // Calculate stats from payments data
        paymentsData.forEach((payment) => {
          const status = payment.paymentStatus?.toLowerCase();
          const amount = parseFloat(payment.paymentAmount || 0);

          // Set currency if available
          if (payment.paymentCurrency) {
            stats.currency = payment.paymentCurrency;
          }

          // Count players
          if (payment.paymentType === "club") {
            if(status === "captured"){
              stats.totalPayers += payment.playersCount || 0;
            }
          } else if (payment.paymentType === "player") {
            if(status === "captured"){
              stats.totalPayers += 1;
            }
          }

          // Calculate amounts based on status
          if (
            status === "captured" ||
            status === "paid" ||
            status === "success"
          ) {
            stats.totalAmount += amount;
            stats.successfulPayments++;

          } else if (status === "refunded") {
            stats.refundAmount += amount;
            stats.refundedPayments++;

            // Count refunded players
            if (payment.paymentType === "club") {
              stats.refundedPayers += payment.playersCount || 0;
            } else if (payment.paymentType === "player") {
              stats.refundedPayers += 1;
            }
          } else if (status === "pending") {
            stats.pendingPayments++;
          } else if (status === "failed") {
            stats.failedPayments++;
          }
        });

        // Calculate platform fee and tournament payout
        stats.platformFeeAmount = (stats.totalAmount * platformFeePercent) / 100;
        stats.tournamentPayout = stats.totalAmount - stats.platformFeeAmount;

        setSummaryStats(stats);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [limit, search, platformFeePercent]
  );

  // Handle club filter search
  const handleClubFilterSearch = () => {
    const clubFilterParams = {};

    if (clubFilters.tournamentTitle) {
      clubFilterParams.tournamentTitle = clubFilters.tournamentTitle;
    }
    if (clubFilters.paymentStatus !== "all") {
      clubFilterParams.paymentStatus = clubFilters.paymentStatus;
    }
    if (clubFilters.startDate) {
      clubFilterParams.startDate = clubFilters.startDate;
    }
    if (clubFilters.endDate) {
      clubFilterParams.endDate = clubFilters.endDate;
    }

    // Validate date range
    if (clubFilters.startDate && clubFilters.endDate && clubFilters.startDate > clubFilters.endDate) {
      toast.error("Start date cannot be after end date");
      return;
    }

    fetchPaymentsWithFilters(1, clubFilterParams);
  };

  // Handle player filter search
  const handlePlayerFilterSearch = () => {
    const playerFilterParams = {};

    if (playerFilters.tournamentTitle) {
      playerFilterParams.tournamentTitle = playerFilters.tournamentTitle;
    }
    if (playerFilters.paymentStatus !== "all") {
      playerFilterParams.paymentStatus = playerFilters.paymentStatus;
    }
    if (playerFilters.startDate) {
      playerFilterParams.startDate = playerFilters.startDate;
    }
    if (playerFilters.endDate) {
      playerFilterParams.endDate = playerFilters.endDate;
    }

    // Validate date range
    if (playerFilters.startDate && playerFilters.endDate && playerFilters.startDate > playerFilters.endDate) {
      toast.error("Start date cannot be after end date");
      return;
    }

    fetchPaymentsWithFilters(1, playerFilterParams);
  };

  const fetchPaymentReport = async () => {
    try {
      const response = await Client.get("/report/payment", {
        params: { ...search },
        responseType: "blob",
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Players_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 4,
        pt: 2,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <BackButton />
      {(clubPayments.length > 0 || playerPayments.length > 0) && (
        <Box
          mb={2}
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          <Button
            size="small"
            variant="contained"
            disabled={tournaments?.length === 0}
            onClick={fetchPaymentReport}
          >
            Download report
          </Button>
        </Box>
      )}

      {/* Search Form */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
      >
        <Typography
          variant="h5"
          gutterBottom
          sx={{ mb: 2, fontWeight: "bold", color: "#3f51b5" }}
        >
          Tournament Payment History
        </Typography>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={6}>
            <Autocomplete
              options={tournaments}
              getOptionLabel={(option) =>
                option && option.title ? option.title.replace(/-/g, " ") : ""
              }
              value={selectedTournament}
              onChange={handleTournamentChange}
              loading={tournamentLoading}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder="Select a tournament"
                  fullWidth
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <React.Fragment>
                        {tournamentLoading ? (
                          <CircularProgress color="inherit" size={20} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </React.Fragment>
                    ),
                  }}
                  sx={{ bgcolor: "white" }}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              label="Transaction ID"
              value={search.transactionId}
              onChange={(e) =>
                setSearch({ ...search, transactionId: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter transaction ID"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Payment Type"
              value={search.paymentType || "all"}
              onChange={(e) =>
                setSearch({ ...search, paymentType: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Select payment type"
            >
              <MenuItem value="all">All Types</MenuItem>
              <MenuItem value="club">Club</MenuItem>
              <MenuItem value="player">Player</MenuItem>
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6} md={12} sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              color="secondary"
              sx={{ width: "40px" }}
              onClick={handleReset}
              disabled={loading}
              title="Reset all filters and refresh data"
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              sx={{
                bgcolor: "#3f51b5",
                textTransform: "none",
                height: "56px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Enhanced Summary Statistics */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontWeight: "bold", color: "#3f51b5" }}
        >
          Payment Summary & Tournament Payout
        </Typography>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Row 1 */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Total Payers
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.totalPayers}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <PersonIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Players
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.totalAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Collected
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#ffebee", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: "#d32f2f" }}>
                  Refunded Players
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#d32f2f",
                  }}
                >
                  {summaryStats.refundedPayers}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#d32f2f" }}>
                  <PersonIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Players
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#ffebee", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: "#d32f2f" }}>
                  Refund Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#d32f2f",
                  }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.refundAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#d32f2f" }}>
                  <RefundIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Refunded
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Row 2 */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#fff3e0", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: "#f57c00" }}>
                  Platform Fee ({platformFeePercent}%)
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#f57c00",
                  }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.platformFeeAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#f57c00" }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Platform Fee
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Tournament Payout
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#2e7d32",
                  }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.tournamentPayout.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#2e7d32" }}>
                  <AccountBalanceIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Net Payout
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f3e5f5", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Tournament
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textTransform: "capitalize",
                    textAlign: "center",
                  }}
                >
                  {selectedTournament && selectedTournament.title
                    ? selectedTournament.title.replace(/-/g, " ")
                    : "All Tournaments"}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <EventIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  {selectedTournament && selectedTournament.startDate
                    ? new Date(selectedTournament.startDate).toLocaleDateString(
                        "en-US",
                        { day: "2-digit", month: "short", year: "numeric" }
                      )
                    : "All Dates"}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f5f5f5", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="textSecondary">
                  Payment Status
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <span style={{ color: "green" }}>
                    ✓ Paid: {summaryStats.successfulPayments}
                  </span>
                </Typography>
                <Typography variant="body2">
                  <span style={{ color: "#d32f2f" }}>
                    ↩ Refunded: {summaryStats.refundedPayments}
                  </span>
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Club Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "club") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Club Payments
          </Typography>

          {/* Club Payments Filters */}
          <Paper sx={{ mb: 2, p: 2, bgcolor: "#f0f8ff", borderRadius: 2 }}>
          
            <Grid container spacing={2} alignItems="center">
                 <Grid item xs={12} sm={3} md={2}>
                <TextField
                  select
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Payment Status"
                  value={clubFilters.paymentStatus}
                  onChange={(e) => {
                    const newStatus = e.target.value;
                    setClubFilters({ ...clubFilters, paymentStatus: newStatus });

                    // Trigger API call immediately when status changes
                    const clubFilterParams = {};
                    if (newStatus !== "all") {
                      clubFilterParams.paymentStatus = newStatus;
                    }
                    if (clubFilters.startDate) {
                      clubFilterParams.startDate = clubFilters.startDate;
                    }
                    if (clubFilters.endDate) {
                      clubFilterParams.endDate = clubFilters.endDate;
                    }
                    fetchPaymentsWithFilters(1, clubFilterParams);
                  }}
                  sx={{ bgcolor: "white" }}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="captured">Paid</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Start Date"
                  value={clubFilters.startDate}
                  onChange={(e) => {
                    setClubFilters({ ...clubFilters, startDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="End Date"
                  value={clubFilters.endDate}
                  onChange={(e) => {
                    if (clubFilters.startDate && e.target.value < clubFilters.startDate) {
                      toast.error("End date cannot be before start date");
                      return;
                    }
                    setClubFilters({ ...clubFilters, endDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ min: clubFilters.startDate }}
                />
              </Grid>

              <Grid item xs={12} sm={12} md={3}>
                <Box sx={{ display: "flex", gap: 1, flexDirection: { xs: "column", sm: "row" } }}>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={handleClubFilterSearch}
                    startIcon={<SearchIcon />}
                    sx={{ bgcolor: "#3f51b5" }}
                  >
                    Search
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      setClubFilters({
                        tournamentTitle: "",
                        paymentStatus: "all",
                        startDate: "",
                        endDate: "",
                      });
                      // Reset to original data
                      fetchPayments(1);
                    }}
                  >
                    Reset
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
          <DynamicTable
            columns={[
              {
                id: "clubName",
                label: "Club Name",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/clubs/${payment.clubId}`}
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.clubName || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "T.Id",
                format: (_, payment) => (payment.paymentTransactionId || "N/A").toUpperCase(),
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "time",
                label: "Time",
                format: (_, payment) => formatTime(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "playersCount",
                label: "Players Count",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    fontSize={"16px"}
                    fontWeight="medium"
                  >
                    {payment.playersCount || "0"}
                  </Typography>
                ),
              },
              {
                id: "registrationType",
                label: "Registration Type",
                format: (_, payment) => payment.registrationType || "N/A",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "captured"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus === "captured"
                      ? "Paid"
                      : payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              // {
              //   id: "tournament",
              //   label: "Tournament",
              //   format: (_, payment) =>
              //     payment?.tournament?.title || payment.tournamentTitle ? (
              //       <Link
              //         to={`/tournaments/${
              //           payment?.tournament?.title || payment.tournamentTitle
              //         }`}
              //         style={{ textDecoration: "none", color: "inherit" }}
              //       >
              //         <Typography
              //           variant="h6"
              //           sx={{
              //             textTransform: "capitalize",
              //             fontWeight: "medium",
              //             textWrap: "balance",
              //             fontSize: "16px",
              //           }}
              //         >
              //           {(payment?.tournament?.title || payment.tournamentTitle)
              //             .toLowerCase()
              //             .replace(/-/g, " ")}
              //         </Typography>
              //       </Link>
              //     ) : (
              //       "N/A"
              //     ),
              // },
            ]}
            data={clubPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}
            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}

      {/* Player Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "player") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Player Payments
          </Typography>

          {/* Player Payments Filters */}
          <Paper sx={{ mb: 2, p: 2, bgcolor: "#f0fff0", borderRadius: 2 }}>
            <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={3} md={2}>
                <TextField
                  select
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Payment Status"
                  value={playerFilters.paymentStatus}
                  onChange={(e) => {
                    const newStatus = e.target.value;
                    setPlayerFilters({ ...playerFilters, paymentStatus: newStatus });

                    // Trigger API call immediately when status changes
                    const playerFilterParams = {};
                    if (newStatus !== "all") {
                      playerFilterParams.paymentStatus = newStatus;
                    }
                    if (playerFilters.startDate) {
                      playerFilterParams.startDate = playerFilters.startDate;
                    }
                    if (playerFilters.endDate) {
                      playerFilterParams.endDate = playerFilters.endDate;
                    }
                    fetchPaymentsWithFilters(1, playerFilterParams);
                  }}
                  sx={{ bgcolor: "white" }}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="captured">Paid</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Start Date"
                  value={playerFilters.startDate}
                  onChange={(e) => {
                    setPlayerFilters({ ...playerFilters, startDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="End Date"
                  value={playerFilters.endDate}
                  onChange={(e) => {
                    if (playerFilters.startDate && e.target.value < playerFilters.startDate) {
                      toast.error("End date cannot be before start date");
                      return;
                    }
                    setPlayerFilters({ ...playerFilters, endDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ min: playerFilters.startDate }}
                />
              </Grid>

              <Grid item xs={12} sm={12} md={3}>
                <Box sx={{ display: "flex", gap: 1, flexDirection: { xs: "column", sm: "row" } }}>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={handlePlayerFilterSearch}
                    startIcon={<SearchIcon />}
                    sx={{ bgcolor: "#3f51b5" }}
                  >
                    Search
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      setPlayerFilters({
                        tournamentTitle: "",
                        paymentStatus: "all",
                        startDate: "",
                        endDate: "",
                      });
                      // Reset to original data
                      fetchPayments(1);
                    }}
                  >
                    Reset
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
          <DynamicTable
            columns={[
              {
                id: "playerName",
                label: "Player",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: "medium", fontSize: "16px" }}
                  >
                    {payment.playerName || "Unknown Player"}
                  </Typography>
                ),
              },
              {
                id: "cbid",
                label: "CBID",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/players/${payment.player?.cbid}`}
                    sx={{ color: "#1976d2", textDecoration: "none" }}
                  >
                    {payment.cbid || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "T.Id",
                format: (_, payment) => payment.paymentTransactionId || "N/A",
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "time",
                label: "Time",
                format: (_, payment) => formatTime(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "paymentMethod",
                label: "Payment Method",
                format: (_, payment) => payment.paymentMode || "Online",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "captured"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus === "captured"
                      ? "Paid"
                      : payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              // {
              //   id: "tournament",
              //   label: "Tournament",
              //   format: (_, payment) =>
              //     payment?.tournament?.title || payment.tournamentTitle ? (
              //       <Link
              //         to={`/tournaments/${
              //           payment?.tournament?.title || payment.tournamentTitle
              //         }`}
              //         style={{ textDecoration: "none", color: "inherit" }}
              //       >
              //         <Typography
              //           variant="h6"
              //           sx={{
              //             textTransform: "capitalize",
              //             fontWeight: "medium",
              //             textWrap: "balance",
              //             fontSize: "16px",
              //           }}
              //         >
              //           {(payment?.tournament?.title || payment.tournamentTitle)
              //             .toLowerCase()
              //             .replace(/-/g, " ")}
              //         </Typography>
              //       </Link>
              //     ) : (
              //       "N/A"
              //     ),
              // },
            ]}
            data={playerPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}
            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}
    </Container>
  );
};

export default TournamentEarningsPage;

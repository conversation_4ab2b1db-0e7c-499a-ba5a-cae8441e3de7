import { Client } from "../../api/client";

// Global state to prevent multiple simultaneous payments
let isProcessing = false;

/**
 * Creates Razorpay order - replaces PayU initiate endpoint
 * @param {Object} params - Payment parameters
 */
export const createOrder = async (params) => {
  const { tournamentId, ageCategory, genderCategory,referral } = params;

  try {
    const response = await Client.post("/payment/create-order", {
      tournamentId,
      action: "tournament_registration",
      ageCategory,
      genderCategory,
      registrationType: "player",
      registerAfterPayment: true,
      referral,
    });

    if (!response.data?.success) {
      throw new Error(response.data?.message || "Order creation failed");
    }

    return response.data.data;
  } catch (error) {
    console.error("Order creation error:", error);
    throw error;
  }
};

/**
 * Verifies payment after successful Razorpay transaction
 * @param {Object} razorpayResponse - Response from Razorpay
 */
export const verifyPayment = async (razorpayResponse) => {
  try {
    const response = await C<PERSON>.post("/payment/verify-payment", {
      razorpay_payment_id: razorpayResponse.razorpay_payment_id,
      razorpay_order_id: razorpayResponse.razorpay_order_id,
      razorpay_signature: razorpayResponse.razorpay_signature,
    });

    if (!response.data?.success) {
      throw new Error(response.data?.message || "Payment verification failed");
    }

    return response.data.data;
  } catch (error) {
    console.error("Payment verification error:", error);
    throw error;
  }
};

/**
 * Extracts user-friendly error message
 * @param {Error} error - Error object
 * @returns {string} - User-friendly error message
 */
export const getErrorMessage = (error) => {
  if (error.response?.data?.error) {
    return error.response.data.error;
  }
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return "An error occurred while processing payment";
};

/**
 * Main payment initiation function - replaces your old PayU initiatePayment function
 * @param {Object} params - Payment parameters and callbacks
 */
export const initiatePayment = async (params, toast) => {
  const {
    tournamentId,
    ageCategory,
    genderCategory,
    onStart,
    onSuccess,
    onError,
    onFinally,
    referral
  } = params;
  const razorpayKey = import.meta.env.VITE_RAZORPAY_KEY;

  if (isProcessing) {
    toast.warn("Payment is already in progress");
    return;
  }

  isProcessing = true;
  onStart?.();

  try {
    // Step 1: Create order (replaces PayU form data)
    const orderData = await createOrder({
      tournamentId,
      ageCategory,
      genderCategory,
      referral
    });


    // Step 2: Open Razorpay checkout
    const options = {
      key: razorpayKey,
      amount: orderData?.checkoutOptions?.amount,
      currency: orderData?.checkoutOptions?.currency || "INR",
      name: orderData?.checkoutOptions?.name || "Tournament Registration",
      description: orderData?.checkoutOptions?.description,
      order_id: orderData?.orderId,
      prefill: {
        name: orderData?.checkoutOptions?.prefill?.name,
        email: orderData?.checkoutOptions?.prefill?.email,
        contact: orderData?.checkoutOptions?.prefill?.contact,
      },
      theme: {
        color: "#3399cc",
      },
      handler: async (response) => {
        try {
          // Step 3: Verify payment (replaces PayU success callback)

          const verificationResult = await verifyPayment(response);
          onSuccess?.(verificationResult);

          toast.success("Payment successful!");
        } catch (error) {
          onError?.(error);
          toast.error("Payment verification failed");
        }
      },
      modal: {
        ondismiss: () => {

          isProcessing = false;
          onFinally?.();
        },
      },
    };

    const rzp = new window.Razorpay(options);
    rzp.open();
  } catch (error) {
    const errorMessage = getErrorMessage(error);
    toast.error(errorMessage);
    onError?.(error);
    console.error("Payment initiation error:", error);
  } finally {
    isProcessing = false;
    onFinally?.();
  }
};

/**
 * Utility function to check if payment is currently processing
 * @returns {boolean} - Whether payment is in progress
 */
export const getIsProcessing = () => isProcessing;

/**
 * Utility function to reset processing state (use with caution)
 */
export const resetProcessingState = () => {
  isProcessing = false;
};

const router = require("express").Router();

const {
  createTournament,
  editTournament,
  getAllTournament,
  getSingleTournament,
  getUpcomingTournament,
  deleteTournament,
  getClubTournaments,
  downlaodBrochure,
  updateTournamentRounds,
  updateTournamentCertificateData,
  generateCertificate,
  uploadTournamentSignature,
  getArbiterInvite,
  cancelArbiterInvite,
  updateArbiter,
  getPlatformfee,
  AttendanceReport,
} = require("../controllers/tournamentController");
const verifyJwt = require("../middlewares/verifyJwt");
const registrationRouter = require("./register");
const tournamentRouter = require("express").Router();
const { uploadFactory, handleUploadError } = require("../utils/s3");
const {
  cancelTournament,
} = require("../controllers/tournamentCancellationController");

const parseJSONFields = (req, res, next) => {
  const fieldsToParse = [
    "maleAgeCategory",
    "femaleAgeCategory",
    "foodFacility",
    "chiefArbiterName",
  ];
  fieldsToParse.forEach((field) => {
    if (req.body[field]) {
      try {
        req.body[field] = JSON.parse(req.body[field]);
      } catch (e) {
        // leave it as is if parsing fails
      }
    }
  });
  next();
};

router.use("/:title/register", registrationRouter);

router.use("/club", tournamentRouter);
router.get("/arbiter-invite/:id",getArbiterInvite );
router.post("/arbiter-invite/cancel",cancelArbiterInvite );
router.post("/arbiter-invite",updateArbiter );
router.post(
  "/",
  verifyJwt,
  uploadFactory.tournament.brochure(),
  handleUploadError,
  parseJSONFields,
  createTournament
);
router.put(
  "/:id",
  verifyJwt,
  uploadFactory.tournament.brochure(),
  handleUploadError,
  parseJSONFields,
  editTournament
);

router.get('/attendance-report',AttendanceReport)
router.get("/", getAllTournament);
router.get("/platform-fee", getPlatformfee);
router.get("/:id", getSingleTournament);
router.get("/:id/brochure", downlaodBrochure);
router.patch("/:id/rounds", verifyJwt, updateTournamentRounds);
router.patch("/:id/certificate-config", verifyJwt, updateTournamentCertificateData);
router.post(
  "/upload-signature",
  verifyJwt,
  uploadFactory.tournament.signature(),
  handleUploadError,
  uploadTournamentSignature
);
router.post("/:id/generate-certificate", generateCertificate);
router.delete("/:id", verifyJwt, deleteTournament);
router.post("/:id/cancel", verifyJwt, cancelTournament);

tournamentRouter.get("/upcoming", verifyJwt, getUpcomingTournament);
tournamentRouter.get("/", verifyJwt, getClubTournaments);

module.exports = router;

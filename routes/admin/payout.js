const express = require("express");
const router = express.Router();
const {
  createPayout,
  getPayoutStatus,
  getClubPayouts,
  calculatePayoutPreview,
} = require("../../controllers/payoutController");
const {
  handleRazorPayWebhook,
  getWebhookEvents,
} = require("../../controllers/webhookController");

const verifyJwt = require("../../middlewares/verifyJwt");

router.post("/create", verifyJwt, createPayout);
router.get("/status/:tournament_id", verifyJwt, getPayoutStatus);
router.get("/list/:clubId", verifyJwt, getClubPayouts);
router.post("/calculate/:tournament_id", verifyJwt, calculatePayoutPreview);



// Middleware to parse raw body for webhook signature verification
const rawBodyParser = express.raw({ type: 'application/json' });

// RazorPay webhook endpoint (no auth required)
router.post("/webhook", rawBodyParser, handleRazorPayWebhook);

// Debug endpoint to see webhook configuration (admin only)
router.get("/events", getWebhookEvents);

module.exports = router;
